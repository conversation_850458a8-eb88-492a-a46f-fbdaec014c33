const Layout = () => import("@/layout/index.vue");

export default {
  path: "/organizations/management",
  name: "Organization",
  component: Layout,
  redirect: "/organizations",
  meta: {
    icon: "ri:building-line",
    title: "Organizations",
    rank: 9,
    // @ts-ignore
    roles: ["super-admin", "admin"]
  },
  children: [
    {
      path: "/organizations",
      name: "OrganizationIndex",
      component: () => import("@/views/organization/index.vue"),
      meta: {
        icon: "ri:building-line",
        title: "Organizations",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
