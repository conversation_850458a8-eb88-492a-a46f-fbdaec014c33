import { reactive, ref } from "vue";
import { $t } from "@/plugins/i18n";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, TranslationFilterProps } from "./type";
import {
  getTranslations,
  saveTranslation,
  deleteTranslation,
  bulkDeleteTranslations
} from "./auth-api";

export function useTranslationHook() {
  /* ***************************
   * Data/State Management
   *************************** */

  const loading = ref(false);
  const filterRef = ref<TranslationFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });

  // Form refs
  const translationFormRef = ref();
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FormItemProps>({
    status: "active"
  });

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetTranslations = async () => {
    try {
      loading.value = true;
      const res = await getTranslations(
        useConvertKeyToSnake({
          ...filterRef.value,
          order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
          page: pagination.currentPage,
          limit: pagination.pageSize
        })
      );
      records.value = useConvertKeyToCamel(res.data);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching translations:", error);
      message($t("Failed to fetch translations"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = () => {
    fnGetTranslations();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    pagination.currentPage = 1;
    fnGetTranslations();
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetTranslations();
  };

  /* ***************************
   * CRUD Operations
   *************************** */

  /* ***************************
   * Delete Operations
   *************************** */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deleteTranslation(row.id);
      message($t("Deleted successfully"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting translation:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDeleteTranslations({ ids: selectedIds });
      message($t("Deleted successfully"), { type: "success" });
      fnGetTranslations();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk deleting translations:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  /* ***************************
   * Form Handlers
   *************************** */

  const handleFilter = async (values: TranslationFilterProps) => {
    filterRef.value = values;
    await fnGetTranslations();
  };

  const handleSubmit = async (values: any) => {
    try {
      loading.value = true;
      const response = await saveTranslation(values);
      if (response.success) {
        message(response.message || $t("Translation saved successfully"), {
          type: "success"
        });
        await fnGetTranslations();
        drawerVisible.value = false;
        drawerValues.value = { translations: {} };
        translationFormRef.value?.resetForm();
      } else {
        message(response.message || $t("Save failed"), {
          type: "error"
        });
      }
    } catch (error) {
      message(
        error.response?.data?.message || error?.message || $t("Save failed"),
        {
          type: "error"
        }
      );
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Return Hook Interface
   *************************** */

  return {
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    sort,
    handleBulkDelete,
    handleDelete,
    fnGetTranslations,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    filterVisible,
    drawerVisible,
    drawerValues,
    translationFormRef,
    handleSubmit,
    handleFilter
  };
}
