#!/usr/bin/env node

/**
 * Simple Widget Build Script
 * Builds the widget without complex dependencies
 */

const fs = require('fs');
const path = require('path');

// Create dist directory
const distDir = path.join(__dirname, 'dist', 'widget');
if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

// Read widget files and create a simple bundle
const widgetFiles = {
  'src/widget/types/index.ts': 'types',
  'src/widget/core/event-emitter.ts': 'core',
  'src/widget/core/widget-loader.ts': 'core',
  'src/widget/api/bot-api.ts': 'api',
  'src/widget/styles/theme-manager.ts': 'styles'
};

// Create a simple UMD bundle
const umdTemplate = `
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ProcmsChatbot = {}));
})(this, (function (exports) {
  'use strict';

  // Event Emitter
  class EventEmitter {
    constructor() {
      this.events = {};
    }

    on(event, callback) {
      if (!this.events[event]) {
        this.events[event] = [];
      }
      this.events[event].push(callback);
    }

    off(event, callback) {
      if (!this.events[event]) return;
      if (callback) {
        this.events[event] = this.events[event].filter(cb => cb !== callback);
      } else {
        this.events[event] = [];
      }
    }

    emit(event, ...args) {
      if (!this.events[event]) return;
      this.events[event].forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error('Event callback error:', error);
        }
      });
    }
  }

  // Theme Manager
  class ThemeManager {
    constructor(container) {
      this.container = container;
      this.currentTheme = 'light';
    }

    setTheme(theme) {
      this.currentTheme = theme;
      this.container.setAttribute('data-procms-theme', theme);
    }

    setCustomTheme(themeConfig) {
      Object.entries(themeConfig).forEach(([key, value]) => {
        const cssVar = '--procms-' + key.replace(/([A-Z])/g, '-$1').toLowerCase();
        this.container.style.setProperty(cssVar, value);
      });
    }
  }

  // Widget Loader
  class WidgetLoader {
    static detectMode() {
      // Simple detection logic
      return 'widget';
    }

    static async loadWidget(config, container) {
      // Create mock widget
      const widget = document.createElement('div');
      widget.className = 'procms-chatbot-widget';
      widget.setAttribute('data-procms-theme', config.theme || 'light');
      
      widget.innerHTML = \`
        <div class="procms-widget-header" style="
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 60px;
          padding: 0 16px;
          background: #3b82f6;
          color: white;
        ">
          <div style="display: flex; align-items: center; gap: 12px;">
            <div style="
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: rgba(255,255,255,0.2);
              display: flex;
              align-items: center;
              justify-content: center;
            ">🤖</div>
            <div>
              <div style="font-weight: 600;">ProcMS Bot</div>
              <div style="font-size: 12px; opacity: 0.8;">Online</div>
            </div>
          </div>
        </div>
        
        <div style="
          flex: 1;
          padding: 16px;
          background: #f9fafb;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #6b7280;
        ">
          <div style="text-align: center;">
            <div style="font-size: 18px; margin-bottom: 8px;">🚀</div>
            <div>Widget loaded successfully!</div>
            <div style="font-size: 12px; margin-top: 4px;">Bot UUID: \${config.botUuid}</div>
          </div>
        </div>
        
        <div style="
          padding: 16px;
          background: white;
          border-top: 1px solid #e5e7eb;
        ">
          <div style="display: flex; gap: 8px;">
            <input type="text" placeholder="Type a message..." style="
              flex: 1;
              padding: 12px 16px;
              border: 1px solid #e5e7eb;
              border-radius: 24px;
              outline: none;
            " />
            <button style="
              width: 40px;
              height: 40px;
              border-radius: 50%;
              background: #3b82f6;
              color: white;
              border: none;
              cursor: pointer;
            ">→</button>
          </div>
        </div>
      \`;

      widget.style.cssText = \`
        display: flex;
        flex-direction: column;
        height: 100%;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      \`;

      if (typeof container === 'string') {
        container = document.querySelector(container);
      }
      
      if (container) {
        container.appendChild(widget);
      }

      return widget;
    }
  }

  // Main Widget Class
  class ProcmsChatbotWidget extends EventEmitter {
    constructor(config) {
      super();
      
      if (!config.botUuid) {
        throw new Error('Bot UUID is required');
      }
      if (!config.apiKey) {
        throw new Error('API Key is required');
      }

      this.config = config;
      this.mounted = false;
      this.container = null;
      this.themeManager = null;
    }

    async mount(selector) {
      try {
        const container = typeof selector === 'string' 
          ? document.querySelector(selector) 
          : selector;

        if (!container) {
          throw new Error('Container not found');
        }

        this.container = container;
        this.widget = await WidgetLoader.loadWidget(this.config, container);
        this.themeManager = new ThemeManager(this.widget);
        this.mounted = true;

        // Apply theme
        if (this.config.theme) {
          this.themeManager.setTheme(this.config.theme);
        }

        this.emit('mounted', { mode: 'widget' });
        this.emit('ready');

        if (this.config.onReady) {
          this.config.onReady();
        }
      } catch (error) {
        this.emit('error', error);
        if (this.config.onError) {
          this.config.onError(error);
        }
        throw error;
      }
    }

    unmount() {
      if (this.widget && this.widget.parentNode) {
        this.widget.parentNode.removeChild(this.widget);
      }
      this.mounted = false;
      this.emit('unmounted');
    }

    isMounted() {
      return this.mounted;
    }

    getMode() {
      return 'widget';
    }

    getConfig() {
      return { ...this.config };
    }

    setTheme(theme) {
      if (this.themeManager) {
        this.themeManager.setTheme(theme);
      }
    }

    setCustomTheme(themeConfig) {
      if (this.themeManager) {
        this.themeManager.setCustomTheme(themeConfig);
      }
    }
  }

  // Static Utility Class
  class ProcmsChatbot {
    static async create(config, selector) {
      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(selector);
      return widget;
    }

    static async createFloatingWidget(config) {
      // Create floating container
      const container = document.createElement('div');
      container.id = 'procms-floating-widget';
      container.style.cssText = \`
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 400px;
        height: 600px;
        z-index: 9999;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        border-radius: 8px;
        overflow: hidden;
      \`;

      document.body.appendChild(container);

      const widget = new ProcmsChatbotWidget(config);
      await widget.mount(container);
      return widget;
    }

    static generateIframeCode(config) {
      const params = new URLSearchParams({
        botUuid: config.botUuid,
        apiKey: config.apiKey,
        theme: config.theme || 'light'
      });

      return \`<iframe 
        src="https://widget.procms.com/embed?\${params}" 
        width="\${config.width || 400}" 
        height="\${config.height || 600}"
        frameborder="0"
        style="border-radius: 8px;">
      </iframe>\`;
    }

    static detectMode() {
      return WidgetLoader.detectMode();
    }
  }

  // Exports
  exports.ProcmsChatbotWidget = ProcmsChatbotWidget;
  exports.ProcmsChatbot = ProcmsChatbot;
  exports.ThemeManager = ThemeManager;

  // Global assignment for UMD
  if (typeof window !== 'undefined') {
    window.ProcmsChatbotWidget = ProcmsChatbotWidget;
    window.ProcmsChatbot = ProcmsChatbot;
  }

}));
`;

// Write UMD bundle
fs.writeFileSync(path.join(distDir, 'procms-chatbot.umd.js'), umdTemplate);

// Create ES module
const esTemplate = `
export { ProcmsChatbotWidget, ProcmsChatbot, ThemeManager } from './procms-chatbot.umd.js';
`;

fs.writeFileSync(path.join(distDir, 'procms-chatbot.es.js'), esTemplate);

// Create CSS file
const cssTemplate = `
/* ProcMS Chatbot Widget Styles */
.procms-chatbot-widget {
  --procms-primary: #3b82f6;
  --procms-primary-hover: #2563eb;
  --procms-widget-bg: #f9fafb;
  --procms-widget-surface: #ffffff;
  --procms-text-primary: #111827;
  --procms-text-secondary: #6b7280;
  --procms-widget-border: #e5e7eb;
  
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: var(--procms-text-primary);
  background: var(--procms-widget-bg);
}

.procms-chatbot-widget[data-procms-theme="dark"] {
  --procms-widget-bg: #1f2937;
  --procms-widget-surface: #374151;
  --procms-text-primary: #f3f4f6;
  --procms-text-secondary: #d1d5db;
  --procms-widget-border: #4b5563;
}

.procms-widget-header {
  background: var(--procms-primary);
  color: white;
}

.procms-message {
  margin-bottom: 16px;
}

.procms-message--user .procms-message__content {
  background: var(--procms-primary);
  color: white;
  margin-left: auto;
}

.procms-message--bot .procms-message__content {
  background: var(--procms-widget-surface);
  color: var(--procms-text-primary);
  border: 1px solid var(--procms-widget-border);
}
`;

fs.writeFileSync(path.join(distDir, 'procms-chatbot.css'), cssTemplate);

// Create package.json for the widget
const packageJson = {
  name: "@procms/chatbot-widget",
  version: "2.0.0",
  description: "ProcMS Chatbot Widget",
  main: "procms-chatbot.umd.js",
  module: "procms-chatbot.es.js",
  types: "types/index.d.ts",
  files: [
    "procms-chatbot.umd.js",
    "procms-chatbot.es.js",
    "procms-chatbot.css",
    "types/"
  ],
  keywords: ["chatbot", "widget", "procms"],
  license: "MIT"
};

fs.writeFileSync(path.join(distDir, 'package.json'), JSON.stringify(packageJson, null, 2));

console.log('✅ Widget built successfully!');
console.log('📁 Output directory:', distDir);
console.log('📦 Files created:');
console.log('  - procms-chatbot.umd.js (UMD bundle)');
console.log('  - procms-chatbot.es.js (ES module)');
console.log('  - procms-chatbot.css (Styles)');
console.log('  - package.json (Package info)');
