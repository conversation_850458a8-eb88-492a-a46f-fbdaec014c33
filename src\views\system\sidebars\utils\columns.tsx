import dayjs from "dayjs";
import { transformI18n } from "@/plugins/i18n";
import { ElTag, ElSwitch } from "element-plus";
import { h } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { isAllEmpty } from "@pureadmin/utils";

// Helper function for date formatting
const formatDateTime = (date: string | null): string => {
  return date ? dayjs(date).format("YYYY-MM-DD HH:mm") : "-";
};

// Menu type mapping
const menuTypeMap = {
  0: { label: "Menu", type: "primary" },
  1: { label: "Iframe", type: "warning" },
  2: { label: "External Link", type: "danger" },
  3: { label: "Button", type: "info" }
} as const;

export const columns: TableColumnList = [
  {
    type: "selection",
    width: 55,
    align: "center",
    hide: false
  },
  {
    headerRenderer: () => transformI18n("Title"),
    prop: "title",
    align: "left",
    minWidth: 200,
    cellRenderer: ({ row }) => (
      <>
        {row.icon && (
          <span class="inline-block mr-2">
            {h(useRenderIcon(row.icon), {
              style: { paddingTop: "1px" }
            })}
          </span>
        )}
        <span>{transformI18n(row.title)}</span>
      </>
    )
  },
  {
    headerRenderer: () => transformI18n("Name"),
    prop: "name",
    align: "left",
    minWidth: 150,
    showOverflowTooltip: true
  },
  {
    headerRenderer: () => transformI18n("Path"),
    prop: "path",
    align: "left",
    minWidth: 180,
    showOverflowTooltip: true,
    formatter: ({ path }) => path || "-"
  },
  {
    headerRenderer: () => transformI18n("Type"),
    prop: "menuType",
    align: "center",
    width: 100,
    cellRenderer: ({ row }) => {
      const { label, type } = menuTypeMap[row.menuType] || menuTypeMap[0];
      return h(
        ElTag,
        {
          type,
          size: "small",
          effect: "plain"
        },
        () => transformI18n(label)
      );
    }
  },
  {
    headerRenderer: () => transformI18n("Parent"),
    prop: "parentId",
    align: "center",
    width: 100,
    formatter: ({ parentId }) => parentId || "-"
  },
  {
    headerRenderer: () => transformI18n("Order"),
    prop: "rank",
    align: "center",
    width: 80,
    sortable: true
  },
  {
    headerRenderer: () => transformI18n("Show Link"),
    prop: "showLink",
    align: "center",
    width: 100,
    cellRenderer: ({ row }) => {
      return h(ElSwitch, {
        modelValue: row.showLink,
        disabled: true,
        size: "small"
      });
    }
  },
  {
    headerRenderer: () => transformI18n("Keep Alive"),
    prop: "keepAlive",
    align: "center",
    width: 100,
    cellRenderer: ({ row }) => {
      return h(ElSwitch, {
        modelValue: row.keepAlive,
        disabled: true,
        size: "small"
      });
    }
  },
  {
    headerRenderer: () => transformI18n("Created At"),
    prop: "createdAt",
    align: "center",
    width: 160,
    sortable: true,
    formatter: ({ createdAt }) => formatDateTime(createdAt)
  },
  {
    headerRenderer: () => transformI18n("Updated At"),
    prop: "updatedAt",
    align: "center",
    width: 160,
    sortable: true,
    formatter: ({ updatedAt }) => formatDateTime(updatedAt)
  },
  {
    headerRenderer: () => transformI18n("Operation"),
    fixed: "right",
    width: 160,
    slot: "operation",
    sortable: false,
    align: "center"
  }
];
