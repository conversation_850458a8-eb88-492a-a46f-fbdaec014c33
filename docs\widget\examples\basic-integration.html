<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Widget Integration - ProcMS Chatbot</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .example {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }
        .example h3 {
            margin-top: 0;
            color: #333;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
        }
        .code-block code {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .widget-container {
            width: 400px;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ProcMS Chatbot Widget - Basic Integration Examples</h1>
        <p>This page demonstrates basic integration methods for the ProcMS Chatbot Widget.</p>

        <!-- Example 1: CDN Integration -->
        <div class="example">
            <h3>1. CDN Integration (Recommended)</h3>
            <p>The easiest way to add the chatbot to your website using CDN.</p>
            
            <div class="code-block">
                <code>
&lt;!-- Add this before closing &lt;/body&gt; tag --&gt;
&lt;script&gt;
    window.PROCMS_CHATBOT_CONFIG = {
        botUuid: 'demo-bot-uuid-123',
        apiKey: 'pk_test_demo_api_key_12345678901234567890',
        theme: 'light',
        position: 'bottom-right',
        autoOpen: false
    };
&lt;/script&gt;
&lt;script src="https://cdn.procms.com/widget/procms-chatbot.umd.js"&gt;&lt;/script&gt;
                </code>
            </div>

            <button class="btn" onclick="loadCDNWidget()">Load CDN Widget</button>
            <div id="status-1" class="status" style="display: none;"></div>
        </div>

        <!-- Example 2: Embedded Widget -->
        <div class="example">
            <h3>2. Embedded Widget</h3>
            <p>Embed the chatbot in a specific container on your page.</p>
            
            <div class="code-block">
                <code>
&lt;div id="chatbot-container" style="width: 400px; height: 600px;"&gt;&lt;/div&gt;

&lt;script&gt;
const widget = await ProcmsChatbot.create({
    botUuid: 'demo-bot-uuid-123',
    apiKey: 'pk_test_demo_api_key_12345678901234567890',
    theme: 'light',
    showHeader: true,
    showAvatar: true
}, '#chatbot-container');
&lt;/script&gt;
                </code>
            </div>

            <div id="chatbot-container" class="widget-container"></div>
            <button class="btn" onclick="loadEmbeddedWidget()">Load Embedded Widget</button>
            <button class="btn" onclick="unloadEmbeddedWidget()">Unload Widget</button>
            <div id="status-2" class="status" style="display: none;"></div>
        </div>

        <!-- Example 3: Floating Widget -->
        <div class="example">
            <h3>3. Floating Widget</h3>
            <p>Create a floating chat button that appears over your content.</p>
            
            <div class="code-block">
                <code>
&lt;script&gt;
const floatingWidget = await ProcmsChatbot.createFloatingWidget({
    botUuid: 'demo-bot-uuid-123',
    apiKey: 'pk_test_demo_api_key_12345678901234567890',
    position: 'bottom-right',
    theme: 'light',
    autoOpen: false
});
&lt;/script&gt;
                </code>
            </div>

            <button class="btn" onclick="loadFloatingWidget()">Create Floating Widget</button>
            <button class="btn" onclick="removeFloatingWidget()">Remove Floating Widget</button>
            <div id="status-3" class="status" style="display: none;"></div>
        </div>

        <!-- Example 4: Iframe Integration -->
        <div class="example">
            <h3>4. Iframe Integration</h3>
            <p>Use iframe for maximum compatibility and isolation.</p>
            
            <div class="code-block">
                <code>
&lt;script&gt;
const iframeCode = ProcmsChatbot.generateIframeCode({
    botUuid: 'demo-bot-uuid-123',
    apiKey: 'pk_test_demo_api_key_12345678901234567890',
    width: 400,
    height: 600,
    theme: 'light'
});

document.getElementById('iframe-container').innerHTML = iframeCode;
&lt;/script&gt;
                </code>
            </div>

            <div id="iframe-container" class="widget-container"></div>
            <button class="btn" onclick="loadIframeWidget()">Load Iframe Widget</button>
            <button class="btn" onclick="unloadIframeWidget()">Unload Iframe</button>
            <div id="status-4" class="status" style="display: none;"></div>
        </div>

        <!-- Example 5: Custom Theme -->
        <div class="example">
            <h3>5. Custom Theme Example</h3>
            <p>Customize the widget appearance with your brand colors.</p>
            
            <div class="code-block">
                <code>
&lt;script&gt;
const widget = new ProcmsChatbotWidget({
    botUuid: 'demo-bot-uuid-123',
    apiKey: 'pk_test_demo_api_key_12345678901234567890'
});

// Apply custom theme
widget.setCustomTheme({
    primaryColor: '#ff6b6b',
    backgroundColor: '#f8f9fa',
    textPrimary: '#2d3436',
    borderRadius: '16px',
    fontFamily: 'Inter, sans-serif'
});

await widget.mount('#custom-theme-container');
&lt;/script&gt;
                </code>
            </div>

            <div id="custom-theme-container" class="widget-container"></div>
            <button class="btn" onclick="loadCustomThemeWidget()">Load Custom Theme Widget</button>
            <button class="btn" onclick="unloadCustomThemeWidget()">Unload Widget</button>
            <div id="status-5" class="status" style="display: none;"></div>
        </div>

        <!-- Example 6: Event Handling -->
        <div class="example">
            <h3>6. Event Handling Example</h3>
            <p>Listen to widget events for analytics and custom behavior.</p>
            
            <div class="code-block">
                <code>
&lt;script&gt;
const widget = new ProcmsChatbotWidget({
    botUuid: 'demo-bot-uuid-123',
    apiKey: 'pk_test_demo_api_key_12345678901234567890',
    onReady: () => console.log('Widget ready'),
    onMessage: (msg) => console.log('New message:', msg),
    onError: (err) => console.error('Error:', err)
});

// Additional event listeners
widget.on('conversation-started', (id) => {
    console.log('Conversation started:', id);
    // Track in analytics
    gtag('event', 'chatbot_conversation_start');
});

widget.on('conversation-ended', (id) => {
    console.log('Conversation ended:', id);
    // Track in analytics
    gtag('event', 'chatbot_conversation_end');
});

await widget.mount('#event-widget-container');
&lt;/script&gt;
                </code>
            </div>

            <div id="event-widget-container" class="widget-container"></div>
            <button class="btn" onclick="loadEventWidget()">Load Event Widget</button>
            <button class="btn" onclick="unloadEventWidget()">Unload Widget</button>
            <div id="status-6" class="status" style="display: none;"></div>
            <div id="event-log" style="background: #f8f9fa; padding: 10px; margin-top: 10px; border-radius: 4px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
        </div>
    </div>

    <!-- Load the widget library -->
    <script src="../../../dist/widget/procms-chatbot.umd.js"></script>
    
    <script>
        // Demo configuration
        const DEMO_CONFIG = {
            botUuid: 'demo-bot-uuid-123',
            apiKey: 'pk_test_demo_api_key_12345678901234567890',
            theme: 'light',
            showHeader: true,
            showAvatar: true,
            width: 400,
            height: 600
        };

        let widgets = {};
        let floatingWidget = null;

        // Helper functions
        function showStatus(id, message, type = 'info') {
            const statusEl = document.getElementById(`status-${id}`);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }

        function logEvent(message) {
            const logEl = document.getElementById('event-log');
            if (logEl) {
                const timestamp = new Date().toLocaleTimeString();
                logEl.innerHTML += `[${timestamp}] ${message}\n`;
                logEl.scrollTop = logEl.scrollHeight;
            }
        }

        // Example 1: CDN Widget (auto-initialize)
        function loadCDNWidget() {
            try {
                // Simulate CDN auto-initialization
                window.PROCMS_CHATBOT_CONFIG = DEMO_CONFIG;
                
                // In real implementation, this would be handled by the CDN script
                ProcmsChatbot.init(DEMO_CONFIG).then(() => {
                    showStatus(1, 'CDN widget auto-initialized successfully!', 'success');
                }).catch(error => {
                    showStatus(1, `Error: ${error.message}`, 'error');
                });
            } catch (error) {
                showStatus(1, `Error: ${error.message}`, 'error');
            }
        }

        // Example 2: Embedded Widget
        async function loadEmbeddedWidget() {
            try {
                showStatus(2, 'Loading embedded widget...', 'info');
                
                const widget = await ProcmsChatbot.create(DEMO_CONFIG, '#chatbot-container');
                widgets[2] = widget;
                
                showStatus(2, 'Embedded widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(2, `Error: ${error.message}`, 'error');
            }
        }

        function unloadEmbeddedWidget() {
            if (widgets[2]) {
                widgets[2].unmount();
                delete widgets[2];
                showStatus(2, 'Widget unloaded.', 'info');
            }
        }

        // Example 3: Floating Widget
        async function loadFloatingWidget() {
            try {
                showStatus(3, 'Creating floating widget...', 'info');
                
                floatingWidget = await ProcmsChatbot.createFloatingWidget({
                    ...DEMO_CONFIG,
                    position: 'bottom-left'  // Different position to avoid conflicts
                });
                
                showStatus(3, 'Floating widget created! Check bottom-left corner.', 'success');
            } catch (error) {
                showStatus(3, `Error: ${error.message}`, 'error');
            }
        }

        function removeFloatingWidget() {
            if (floatingWidget) {
                floatingWidget.unmount();
                const container = document.getElementById('procms-floating-widget');
                if (container) container.remove();
                floatingWidget = null;
                showStatus(3, 'Floating widget removed.', 'info');
            }
        }

        // Example 4: Iframe Widget
        function loadIframeWidget() {
            try {
                showStatus(4, 'Loading iframe widget...', 'info');
                
                const iframeCode = ProcmsChatbot.generateIframeCode(DEMO_CONFIG);
                document.getElementById('iframe-container').innerHTML = iframeCode;
                
                showStatus(4, 'Iframe widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(4, `Error: ${error.message}`, 'error');
            }
        }

        function unloadIframeWidget() {
            document.getElementById('iframe-container').innerHTML = '';
            showStatus(4, 'Iframe widget removed.', 'info');
        }

        // Example 5: Custom Theme Widget
        async function loadCustomThemeWidget() {
            try {
                showStatus(5, 'Loading custom theme widget...', 'info');
                
                const widget = new ProcmsChatbotWidget(DEMO_CONFIG);
                
                // Apply custom theme
                widget.setCustomTheme({
                    primaryColor: '#ff6b6b',
                    backgroundColor: '#f8f9fa',
                    textPrimary: '#2d3436',
                    borderRadius: '16px',
                    fontFamily: 'Inter, sans-serif'
                });
                
                await widget.mount('#custom-theme-container');
                widgets[5] = widget;
                
                showStatus(5, 'Custom theme widget loaded successfully!', 'success');
            } catch (error) {
                showStatus(5, `Error: ${error.message}`, 'error');
            }
        }

        function unloadCustomThemeWidget() {
            if (widgets[5]) {
                widgets[5].unmount();
                delete widgets[5];
                showStatus(5, 'Custom theme widget unloaded.', 'info');
            }
        }

        // Example 6: Event Widget
        async function loadEventWidget() {
            try {
                showStatus(6, 'Loading event widget...', 'info');
                
                const widget = new ProcmsChatbotWidget({
                    ...DEMO_CONFIG,
                    onReady: () => logEvent('Widget ready'),
                    onMessage: (msg) => logEvent(`New message: ${msg.type} - ${msg.content}`),
                    onError: (err) => logEvent(`Error: ${err.message}`)
                });

                // Additional event listeners
                widget.on('mounted', ({ mode }) => {
                    logEvent(`Widget mounted in ${mode} mode`);
                });

                widget.on('bot-loaded', (botConfig) => {
                    logEvent(`Bot loaded: ${botConfig.name}`);
                });

                widget.on('conversation-started', (id) => {
                    logEvent(`Conversation started: ${id}`);
                });

                widget.on('conversation-ended', (id) => {
                    logEvent(`Conversation ended: ${id}`);
                });

                await widget.mount('#event-widget-container');
                widgets[6] = widget;
                
                showStatus(6, 'Event widget loaded successfully! Check event log below.', 'success');
            } catch (error) {
                showStatus(6, `Error: ${error.message}`, 'error');
            }
        }

        function unloadEventWidget() {
            if (widgets[6]) {
                widgets[6].unmount();
                delete widgets[6];
                showStatus(6, 'Event widget unloaded.', 'info');
                logEvent('Widget unloaded');
            }
        }

        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });

        // Log widget library info
        console.log('ProcMS Chatbot Widget Library loaded');
        console.log('Available classes:', typeof ProcmsChatbotWidget !== 'undefined' ? { ProcmsChatbotWidget, ProcmsChatbot } : 'Not loaded yet');
    </script>
</body>
</html>
