import { reactive } from "vue";
import type { FormRules } from "element-plus";
import { $t } from "@/plugins/i18n";
import { 
  createEmailValidator,
  createPhoneValidator,
  createPasswordValidator,
  createConfirmPasswordValidator
} from "@/views/auth/utils/validation";

// Custom validators for profile
const nameValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("This field is required")));
    return;
  }

  if (value.length < 2) {
    callback(new Error($t("Name must be at least 2 characters")));
    return;
  }

  if (value.length > 50) {
    callback(new Error($t("Name must not exceed 50 characters")));
    return;
  }

  // Check for valid name characters (letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-ZÀ-ÿ\s\-']+$/;
  if (!nameRegex.test(value)) {
    callback(new Error($t("Name can only contain letters, spaces, hyphens, and apostrophes")));
    return;
  }

  callback();
};

const addressValidator = (_rule: any, value: string, callback: Function) => {
  if (value && value.length > 255) {
    callback(new Error($t("Address must not exceed 255 characters")));
    return;
  }
  callback();
};

const birthdayValidator = (_rule: any, value: string, callback: Function) => {
  if (value) {
    const birthday = new Date(value);
    const today = new Date();
    const age = today.getFullYear() - birthday.getFullYear();
    
    if (birthday > today) {
      callback(new Error($t("Birthday cannot be in the future")));
      return;
    }
    
    if (age > 150) {
      callback(new Error($t("Please enter a valid birthday")));
      return;
    }
  }
  callback();
};

const currentPasswordValidator = (_rule: any, value: string, callback: Function) => {
  if (!value || value.trim() === "") {
    callback(new Error($t("Current password is required")));
    return;
  }
  callback();
};

export const profileRules = reactive<FormRules>({
  firstName: [{ validator: nameValidator, trigger: "blur" }],
  lastName: [{ validator: nameValidator, trigger: "blur" }],
  email: [{ validator: createEmailValidator(), trigger: "blur" }],
  phone: [{ validator: createPhoneValidator({ required: false }), trigger: "blur" }],
  address: [{ validator: addressValidator, trigger: "blur" }],
  birthday: [{ validator: birthdayValidator, trigger: "blur" }],
  gender: [
    {
      required: false,
      message: $t("Please select gender"),
      trigger: "change"
    }
  ],
  currentPassword: [{ validator: currentPasswordValidator, trigger: "blur" }],
  newPassword: [
    { 
      validator: createPasswordValidator({
        minLength: 8,
        requireNumbers: true,
        requireSymbols: false,
        requireUppercase: false,
        requireLowercase: false,
        useSettings: true
      }), 
      trigger: "blur" 
    }
  ],
  confirmPassword: [
    { 
      validator: createConfirmPasswordValidator("newPassword"), 
      trigger: "blur" 
    }
  ]
});
