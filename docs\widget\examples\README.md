# Integration Examples

This directory contains comprehensive examples for integrating the ProcMS Chatbot Widget with different frameworks and use cases.

## 📁 Available Examples

### Basic Integration
- **[basic-integration.html](./basic-integration.html)** - Live HTML examples with CDN, embedded, floating, and iframe integration methods
- **[advanced-integration.html](./advanced-integration.html)** - Advanced patterns including A/B testing, analytics, and conditional loading

### Framework-Specific Guides
- **[react-integration.md](./react-integration.md)** - Complete React integration guide with hooks, context, and TypeScript
- **[vue-integration.md](./vue-integration.md)** - Vue.js integration for both Vue 2 and Vue 3 with Composition API
- **[angular-integration.md](./angular-integration.md)** - Angular integration with services and components *(Coming Soon)*

### Specialized Examples
- **[wordpress-integration.md](./wordpress-integration.md)** - WordPress plugin integration *(Coming Soon)*
- **[shopify-integration.md](./shopify-integration.md)** - Shopify theme integration *(Coming Soon)*
- **[nextjs-integration.md](./nextjs-integration.md)** - Next.js SSR/SSG integration *(Coming Soon)*

## 🚀 Quick Start Examples

### 1. CDN Integration (Fastest)

```html
<!DOCTYPE html>
<html>
<head>
    <title>My Website</title>
</head>
<body>
    <!-- Your content -->
    
    <!-- ProcMS Chatbot Widget -->
    <script>
        window.PROCMS_CHATBOT_CONFIG = {
            botUuid: 'your-bot-uuid-here',
            apiKey: 'pk_live_your_api_key_here',
            theme: 'light',
            position: 'bottom-right'
        };
    </script>
    <script src="https://cdn.procms.com/widget/procms-chatbot.umd.js"></script>
</body>
</html>
```

### 2. NPM Integration

```bash
npm install @procms/chatbot-widget
```

```javascript
import { ProcmsChatbot } from '@procms/chatbot-widget';
import '@procms/chatbot-widget/dist/style.css';

const widget = await ProcmsChatbot.create({
    botUuid: 'your-bot-uuid-here',
    apiKey: 'pk_live_your_api_key_here'
}, '#chatbot-container');
```

### 3. React Component

```jsx
import React, { useEffect, useRef } from 'react';
import { ProcmsChatbot } from '@procms/chatbot-widget';

const ChatbotWidget = ({ botUuid, apiKey }) => {
  const containerRef = useRef(null);
  const widgetRef = useRef(null);

  useEffect(() => {
    const loadWidget = async () => {
      widgetRef.current = await ProcmsChatbot.create({
        botUuid,
        apiKey
      }, containerRef.current);
    };

    loadWidget();

    return () => {
      if (widgetRef.current) {
        widgetRef.current.unmount();
      }
    };
  }, [botUuid, apiKey]);

  return <div ref={containerRef} style={{ width: '400px', height: '600px' }} />;
};
```

### 4. Vue Component

```vue
<template>
  <div ref="container" style="width: 400px; height: 600px;"></div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ProcmsChatbot } from '@procms/chatbot-widget'

const props = defineProps(['botUuid', 'apiKey'])
const container = ref(null)
let widget = null

onMounted(async () => {
  widget = await ProcmsChatbot.create({
    botUuid: props.botUuid,
    apiKey: props.apiKey
  }, container.value)
})

onUnmounted(() => {
  if (widget) {
    widget.unmount()
  }
})
</script>
```

## 🎯 Integration Patterns

### Floating Widget (Most Popular)

Creates a chat button that floats over your content:

```javascript
const widget = await ProcmsChatbot.createFloatingWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    position: 'bottom-right',
    autoOpen: false
});
```

**Best for:**
- Customer support
- Lead generation
- General website assistance

### Embedded Widget

Embeds the chat interface directly in your page:

```javascript
const widget = await ProcmsChatbot.create({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx'
}, '#chat-container');
```

**Best for:**
- Dedicated support pages
- Dashboard integrations
- Custom layouts

### Iframe Integration

Uses iframe for maximum compatibility:

```javascript
const iframeCode = ProcmsChatbot.generateIframeCode({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    width: 400,
    height: 600
});
document.getElementById('container').innerHTML = iframeCode;
```

**Best for:**
- Sites with CSS conflicts
- Third-party integrations
- Legacy systems

## 🎨 Customization Examples

### Theme Customization

```javascript
// Built-in themes
widget.setTheme('dark');

// Custom theme
widget.setCustomTheme({
    primaryColor: '#ff6b6b',
    backgroundColor: '#f8f9fa',
    textPrimary: '#2d3436',
    borderRadius: '16px',
    fontFamily: 'Inter, sans-serif'
});
```

### CSS Customization

```css
.procms-chatbot-widget {
    --procms-primary: #your-brand-color;
    --procms-widget-border-radius: 12px;
    --procms-font-family: 'Your Font', sans-serif;
}

/* Custom header */
.procms-widget-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
}
```

### Event Handling

```javascript
const widget = new ProcmsChatbotWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    onMessage: (message) => {
        // Track in analytics
        gtag('event', 'chatbot_message', {
            message_type: message.type
        });
    },
    onConversationStarted: (id) => {
        console.log('Conversation started:', id);
    }
});
```

## 📊 Advanced Use Cases

### A/B Testing

```javascript
const variant = Math.random() < 0.5 ? 'A' : 'B';
const configs = {
    A: { theme: 'light', position: 'bottom-right' },
    B: { theme: 'dark', position: 'bottom-left' }
};

const widget = await ProcmsChatbot.createFloatingWidget({
    ...configs[variant],
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    metadata: { variant }
});
```

### Multiple Bots

```javascript
// Support bot
const supportWidget = await ProcmsChatbot.create({
    botUuid: 'support-bot-uuid',
    apiKey: 'pk_live_support_key'
}, '#support-chat');

// Sales bot
const salesWidget = await ProcmsChatbot.create({
    botUuid: 'sales-bot-uuid',
    apiKey: 'pk_live_sales_key'
}, '#sales-chat');
```

### Conditional Loading

```javascript
// Load different bots based on page
const pageBots = {
    '/support': 'support-bot-uuid',
    '/pricing': 'sales-bot-uuid',
    '/': 'general-bot-uuid'
};

const botUuid = pageBots[window.location.pathname] || pageBots['/'];
const widget = await ProcmsChatbot.createFloatingWidget({
    botUuid,
    apiKey: 'pk_live_xxx'
});
```

### User Context

```javascript
const widget = new ProcmsChatbotWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    userId: getCurrentUserId(),
    metadata: {
        userName: getCurrentUserName(),
        userPlan: getCurrentUserPlan(),
        pageUrl: window.location.href
    }
});
```

## 🔧 Development Tips

### Debug Mode

```javascript
// Enable debug logging
window.PROCMS_DEBUG = true;

// Check widget status
console.log('Widget config:', widget.getConfig());
console.log('Widget mode:', widget.getMode());
```

### Error Handling

```javascript
widget.on('error', (error) => {
    console.error('Widget error:', error);
    
    // Show user-friendly message
    showNotification('Chat temporarily unavailable');
    
    // Track in analytics
    gtag('event', 'widget_error', {
        error_message: error.message
    });
});
```

### Performance Monitoring

```javascript
const startTime = performance.now();

widget.on('ready', () => {
    const loadTime = performance.now() - startTime;
    console.log(`Widget loaded in ${loadTime.toFixed(2)}ms`);
    
    gtag('event', 'widget_performance', {
        load_time: Math.round(loadTime)
    });
});
```

## 📱 Mobile Optimization

### Responsive Configuration

```javascript
const isMobile = window.innerWidth <= 768;

const widget = await ProcmsChatbot.createFloatingWidget({
    botUuid: 'bot-123',
    apiKey: 'pk_live_xxx',
    width: isMobile ? '100vw' : 400,
    height: isMobile ? '100vh' : 600,
    position: isMobile ? 'center' : 'bottom-right'
});
```

### Touch Optimization

```css
/* Ensure touch targets are large enough */
.procms-chatbot-widget {
    --procms-button-height: 48px;
    --procms-input-height: 48px;
}

/* Prevent zoom on iOS */
.procms-input-area__input {
    font-size: 16px !important;
}
```

## 🧪 Testing

### Unit Testing

```javascript
// Mock the widget for testing
jest.mock('@procms/chatbot-widget', () => ({
    ProcmsChatbot: {
        create: jest.fn(() => Promise.resolve({
            unmount: jest.fn(),
            setTheme: jest.fn()
        }))
    }
}));
```

### Integration Testing

```javascript
// Test widget loading
describe('Chatbot Widget', () => {
    test('loads successfully', async () => {
        const widget = await ProcmsChatbot.create({
            botUuid: 'test-bot',
            apiKey: 'pk_test_key'
        }, '#test-container');
        
        expect(widget).toBeDefined();
        expect(widget.isMounted()).toBe(true);
    });
});
```

## 📚 Additional Resources

- **[API Reference](../api-reference.md)** - Complete API documentation
- **[Migration Guide](../migration.md)** - Upgrade from older versions
- **[Troubleshooting](../troubleshooting.md)** - Common issues and solutions
- **[Theme System](../../widget/styles/README.md)** - CSS and theming guide

## 🆘 Need Help?

1. **Check the examples** in this directory
2. **Review the troubleshooting guide** for common issues
3. **Enable debug mode** to see detailed logs
4. **Contact support** with specific error details

## 🤝 Contributing

Found an issue or want to add an example?

1. Fork the repository
2. Create a feature branch
3. Add your example with documentation
4. Submit a pull request

## 📄 License

All examples are provided under the MIT License. Feel free to use them in your projects.
