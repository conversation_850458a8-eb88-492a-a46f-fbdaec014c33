import { reactive, ref, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, PostFilterProps } from "./type";
import {
  getPosts,
  getPostsDropdown,
  createPost,
  updatePostById,
  bulkDeletePosts,
  deletePost,
  deletePostPermanent,
  bulkDeletePostsPermanent,
  restorePost,
  bulkRestorePosts
} from "./auth-api";

export function usePostHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const filterRef = ref<PostFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "draft"
  });
  const postFormRef = ref();

  /* ***************************
   * API Data Fetching
   *************************** */

  const fnGetPosts = async () => {
    try {
      loading.value = true;
      const params = useConvertKeyToSnake({
        ...filterRef.value,
        order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
        page: pagination.currentPage,
        limit: pagination.pageSize
      });
      const res = await getPosts(params);
      records.value = useConvertKeyToCamel(res.data.records);
      pagination.total = res.total;
    } catch (error) {
      console.error("Error fetching posts:", error);
      message($t("Failed to fetch posts"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * API CRUD Operations
   *************************** */

  const handleSubmit = async (values: FieldValues) => {
    try {
      loading.value = true;
      if (values.id) {
        await updatePostById(values.id, values);
        message($t("Updated successfully"), { type: "success" });
      } else {
        await createPost(values);
        message($t("Created successfully"), { type: "success" });
      }
      drawerVisible.value = false;
      fnGetPosts();
    } catch (error) {
      console.error("Error submitting post:", error);
      message($t("Operation failed"), { type: "error" });
    } finally {
      loading.value = false;
    }
  };

  /* ***************************
   * Table Event Handlers
   *************************** */

  const fnHandleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const fnHandlePageChange = (val: number) => {
    pagination.currentPage = val;
    fnGetPosts();
  };

  const fnHandleSizeChange = (val: number) => {
    pagination.pageSize = val;
    fnGetPosts();
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetPosts();
  };
  /* ***************************
   * Delete handlers and actions
   *************************** */

  const handleDelete = async (row: any) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to delete this item?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await deletePost(row.id);
      message($t("Deleted successfully"), { type: "success" });
      fnGetPosts();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error deleting post:", error);
        message($t("Delete failed"), { type: "error" });
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move selected items to trash?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDeletePosts({ ids });
      if (response.success) {
        message(response.message || $t("Bulk delete successful"), {
          type: "success"
        });
        await fnGetPosts();
      } else {
        message(response?.message || $t("Bulk delete failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Bulk delete failed"),
          { type: "error" }
        );
      }
    }
  };

  /* ***************************
   * Destroy handlers and actions
   *************************** */

  const handleBulkDestroy = async (ids?: number[]) => {
    const selectedIds = ids || multipleSelection.value.map(item => item.id);
    if (selectedIds.length === 0) {
      message($t("Please select items to destroy"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently destroy selected items?"),
        $t("Warning"),
        {
          confirmButtonText: $t("Confirm"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      await bulkDestroyPosts({ ids: selectedIds });
      message($t("Destroyed successfully"), { type: "success" });
      fnGetPosts();
    } catch (error) {
      if (error !== "cancel") {
        console.error("Error bulk destroying posts:", error);
        message($t("Destroy failed"), { type: "error" });
      }
    }
  };

  /* ***************************
   * Restore handlers and actions
   *************************** */

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirmation"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkRestorePosts({ ids });
      if (response.success) {
        message(response.message || $t("Bulk restore successful"), {
          type: "success"
        });
        await fnGetPosts();
      } else {
        message(response?.message || $t("Bulk restore failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Bulk restore failed"),
          { type: "error" }
        );
      }
    }
  };
  /* ***************************
   * UI Action Handlers
   *************************** */

  const handleEdit = (row: any) => {
    drawerValues.value = { ...row };
    drawerVisible.value = true;
  };

  /* ***************************
   * Form Handlers
   *************************** */

  const handleFilter = (values: PostFilterProps) => {
    filterRef.value = values;
    pagination.currentPage = 1;
    fnGetPosts();
  };

  /* ***************************
   * Lifecycle
   *************************** */

  onMounted(() => {
    fnGetPosts();
  });

  /* ***************************
   * Return Hook Interface
   *************************** */

  /*
   ***************************
   *   UI Action Handlers - Permanent Delete
   ***************************
   */
  const handlePermanentDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete this item? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );

      const response = await deletePostPermanent(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetPosts();
      } else {
        message(response?.message || $t("Delete failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Delete failed"),
          { type: "error" }
        );
      }
    }
  };

  const handleBulkPermanentDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t(
          "Are you sure you want to permanently delete selected items? This action cannot be undone."
        ),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );

      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDeletePostsPermanent({ ids });
      if (response.success) {
        message(response.message || $t("Bulk delete successful"), {
          type: "success"
        });
        await fnGetPosts();
      } else {
        message(response?.message || $t("Bulk delete failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Bulk delete failed"),
          { type: "error" }
        );
      }
    }
  };

  /*
   ***************************
   *   UI Action Handlers - Restore
   ***************************
   */
  const handleRestore = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirmation"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      const response = await restorePost(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetPosts();
      } else {
        message(response?.message || $t("Restore failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message ||
            error?.message ||
            $t("Restore failed"),
          { type: "error" }
        );
      }
    }
  };

  /*
   ***************************
   *   Return Hook Interface
   ***************************
   */
  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    // Event handlers
    handleBulkDelete,
    handleDelete,
    fnGetPosts,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    // Form related
    filterVisible,
    drawerVisible,
    drawerValues,
    postFormRef,
    handleSubmit,
    handleFilter,
    handleBulkPermanentDelete,
    handleBulkRestore,
    handlePermanentDelete,
    handleRestore
  };
}
