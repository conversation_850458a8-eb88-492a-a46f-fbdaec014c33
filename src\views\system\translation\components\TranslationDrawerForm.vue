<script setup lang="ts">
import { ref, computed } from "vue";
import { $t } from "@/plugins/i18n";

interface Props {
  visible?: boolean;
  values?: Record<string, any>;
  availableLocales?: Array<{ code: string; name: string }>;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "update:values", values: Record<string, any>): void;
  (e: "submit", values: Record<string, any>): void;
  (e: "close"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  values: () => ({ translations: {} }),
  availableLocales: () => [
    { code: "en", name: "English" },
    { code: "vi", name: "Vietnamese" }
  ]
});

const emit = defineEmits<Emits>();

const formRef = ref();

const isEdit = computed(() => props.values?.id != null);

// Computed properties for v-model
const visibleModel = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value)
});

const valuesModel = computed({
  get: () => props.values || { translations: {} },
  set: value => emit("update:values", value)
});

// Removed columns as we're using el-form directly

const handleSubmit = () => {
  formRef.value?.validate((valid: boolean) => {
    if (valid) {
      emit("submit", valuesModel.value);
    }
  });
};

const handleReset = () => {
  formRef.value?.resetFields();
  emit("update:values", {
    key: "",
    group: "",
    translations: {}
  });
};

const handleClose = () => {
  emit("close");
  emit("update:visible", false);
};

const resetForm = () => {
  formRef.value?.resetFields();
};

defineExpose({
  resetForm
});
</script>

<template>
  <el-drawer
    v-model="visibleModel"
    :title="isEdit ? $t('Edit Translation') : $t('Create Translation')"
    size="600px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="valuesModel"
      label-width="120px"
      label-position="right"
    >
      <el-form-item
        :label="$t('Translation Key')"
        prop="key"
        :rules="[
          { required: true, message: $t('Translation key is required') }
        ]"
      >
        <el-input
          v-model="valuesModel.key"
          :placeholder="$t('Enter translation key (e.g., auth.login)')"
        />
      </el-form-item>

      <el-form-item
        :label="$t('Group')"
        prop="group"
        :rules="[{ required: true, message: $t('Group is required') }]"
      >
        <el-input
          v-model="valuesModel.group"
          :placeholder="$t('Enter group (e.g., auth, validation)')"
        />
      </el-form-item>

      <el-divider>{{ $t("Translations") }}</el-divider>

      <!-- Dynamic locale fields -->
      <div v-for="locale in availableLocales" :key="locale.code" class="mb-4">
        <el-form-item :label="`${locale.name} (${locale.code.toUpperCase()})`">
          <el-input
            v-model="valuesModel.translations[locale.code]"
            type="textarea"
            :rows="2"
            :placeholder="`${$t('Enter translation for')} ${locale.name}`"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="flex justify-end gap-4">
        <el-button @click="handleClose">
          {{ $t("Cancel") }}
        </el-button>
        <el-button @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ isEdit ? $t("Update") : $t("Create") }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
