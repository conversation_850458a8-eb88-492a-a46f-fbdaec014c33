import dayjs from "dayjs";
import { h } from "vue";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";

export const columns: TableColumnList = [
  {
    type: "selection",
    width: "30px",
    sortable: false
  },
  {
    type: "index",
    width: 40,
    headerRenderer: () => $t("No.")
  },
  {
    prop: "title",
    align: "left",
    sortable: "custom",
    width: 190,
    headerRenderer: () => $t("Title")
  },
  {
    prop: "category.name",
    align: "left",
    minWidth: 150,
    headerRenderer: () => $t("Category"),
    showOverflowTooltip: false,
    cellRenderer: ({ row }) => row.category?.name || "-"
  },
  {
    prop: "featured",
    align: "center",
    width: 100,
    sortable: true,
    headerRenderer: () => $t("Featured"),
    cellRenderer: ({ row }) => {
      return h(
        ElTag,
        {
          type: row.featured ? "success" : "info",
          size: "small"
        },
        () => $t(row.featured ? "YES" : "NO")
      );
    }
  },
  {
    prop: "status",
    sortable: false,
    align: "center",
    width: 100,
    headerRenderer: () => $t("Status"),
    cellRenderer: ({ row }) => {
      const statusColors = {
        draft: {
          type: "warning",
          text: $t("Draft"),
          class: "bg-yellow-100 text-yellow-800",
          icon: "ri:edit-line",
          iconClass: "text-yellow-600"
        },
        published: {
          type: "success",
          text: $t("Published"),
          class: "bg-green-100 text-green-800",
          icon: "ri:checkbox-circle-fill",
          iconClass: "text-green-600"
        },
        archived: {
          type: "info",
          text: $t("Archived"),
          class: "bg-gray-100 text-gray-800",
          icon: "ri:archive-line",
          iconClass: "text-gray-600"
        }
      };

      const config = statusColors[row.status] || statusColors.draft;

      return h(
        "span",
        {
          class: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.class}`
        },
        [
          h(IconifyIconOnline, {
            icon: config.icon,
            class: `w-3 h-3 mr-1.5 ${config.iconClass}`
          }),
          config.text
        ]
      );
    }
  },
  {
    prop: "createdAt",
    width: 160,
    headerRenderer: () => $t("Created at"),
    formatter: (row: Record<string, any>) => formatDateTime(row.createdAt),
    sortable: true
  },
  {
    prop: "publishedAt",
    width: 160,
    headerRenderer: () => $t("Published at"),
    formatter: (row: Record<string, any>) => formatDateTime(row.publishedAt),
    sortable: true
  },
  {
    label: "",
    fixed: "right",
    width: 160,
    slot: "operation",
    sortable: false,
    align: "center"
  }
];
