<script setup lang="ts">
import { ref, computed } from "vue";
import { $t } from "@/plugins/i18n";

interface Props {
  visible?: boolean;
  values?: Record<string, any>;
}

interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "update:values", values: Record<string, any>): void;
  (e: "submit", values: Record<string, any>): void;
  (e: "reset"): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  values: () => ({})
});

const emit = defineEmits<Emits>();

const formRef = ref();

// Computed properties for v-model
const visibleModel = computed({
  get: () => props.visible,
  set: value => emit("update:visible", value)
});

const valuesModel = computed({
  get: () => props.values || {},
  set: value => emit("update:values", value)
});

const handleSubmit = () => {
  emit("submit", valuesModel.value);
  emit("update:visible", false);
};

const handleReset = () => {
  emit("reset");
  emit("update:visible", false);
};

const handleClose = () => {
  emit("update:visible", false);
};
</script>

<template>
  <el-dialog
    v-model="visibleModel"
    :title="$t('Filter Translations')"
    width="500px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="valuesModel"
      label-width="100px"
      label-position="right"
    >
      <el-form-item :label="$t('Key')">
        <el-input
          v-model="valuesModel.key"
          :placeholder="$t('Search by key')"
        />
      </el-form-item>

      <el-form-item :label="$t('Value')">
        <el-input
          v-model="valuesModel.value"
          :placeholder="$t('Search by value')"
        />
      </el-form-item>

      <el-form-item :label="$t('Group')">
        <el-input
          v-model="valuesModel.group"
          :placeholder="$t('Search by group')"
        />
      </el-form-item>

      <el-form-item :label="$t('Locale')">
        <el-select
          v-model="valuesModel.locale"
          :placeholder="$t('Select locale')"
          clearable
        >
          <el-option label="All" value="" />
          <el-option label="English" value="en" />
          <el-option label="Vietnamese" value="vi" />
          <el-option label="Chinese" value="zh" />
          <el-option label="Japanese" value="ja" />
        </el-select>
      </el-form-item>

      <el-form-item :label="$t('Trashed')">
        <el-radio-group v-model="valuesModel.isTrashed">
          <el-radio value="no">{{ $t("No") }}</el-radio>
          <el-radio value="yes">{{ $t("Yes") }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="flex justify-end gap-4">
        <el-button @click="handleClose">
          {{ $t("Cancel") }}
        </el-button>
        <el-button @click="handleReset">
          {{ $t("Reset") }}
        </el-button>
        <el-button type="primary" @click="handleSubmit">
          {{ $t("Filter") }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>
