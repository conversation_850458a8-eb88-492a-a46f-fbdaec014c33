<script setup lang="ts">
import Motion from "./utils/motion";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { profileRules } from "./utils/rule";
import { computed, reactive, ref, onMounted } from "vue";
import { useNav } from "@/layout/hooks/useNav";
import type { FormInstance } from "element-plus";
import { useLayout } from "@/layout/hooks/useLayout";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  updateCurrentUser,
  updateCurrentUserPassword,
  updateCurrentUserAvatar
} from "@/views/auth/api/auth-api";
import type { UserResult } from "@/views/auth/api/auth-api";
import User from "@iconify-icons/ri/user-3-fill";
import Mail from "@iconify-icons/ri/mail-fill";
import Phone from "@iconify-icons/ri/phone-fill";
import MapPin from "@iconify-icons/ri/map-pin-fill";
import Calendar from "@iconify-icons/ri/calendar-fill";
import Lock from "@iconify-icons/ri/lock-fill";
import Camera from "@iconify-icons/ri/camera-fill";
import Save from "@iconify-icons/ri/save-fill";
import { $t } from "@/plugins/i18n";
import { IconifyIconOnline } from "@/components/ReIcon";

defineOptions({
  name: "Profile"
});

const router = useRouter();
const loading = ref(false);
const passwordLoading = ref(false);
const avatarLoading = ref(false);
const disabled = ref(false);
const profileFormRef = ref<FormInstance>();
const passwordFormRef = ref<FormInstance>();
const avatarUploadRef = ref();

const { initStorage } = useLayout();
initStorage();

const userStore = useUserStoreHook();
const currentUser = computed(() => userStore.userInfo);

// Computed properties
const fullName = computed(() => {
  if (currentUser.value?.fullName) {
    return currentUser.value.fullName;
  }
  if (currentUser.value?.firstName && currentUser.value?.lastName) {
    return `${currentUser.value.firstName} ${currentUser.value.lastName}`;
  }
  return currentUser.value?.username || $t("User Profile");
});

// Profile form data
const profileForm = reactive({
  username: "",
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  address: "",
  birthday: "",
  gender: "male"
});

// Password form data
const passwordForm = reactive({
  currentPassword: "",
  newPassword: "",
  confirmPassword: ""
});

// Load user data
const loadUserData = () => {
  if (currentUser.value) {
    profileForm.username = currentUser.value.username || "";
    profileForm.firstName = currentUser.value.firstName || "";
    profileForm.lastName = currentUser.value.lastName || "";
    profileForm.email = currentUser.value.email || "";
    profileForm.phone = currentUser.value.phone || "";
    profileForm.address = currentUser.value.address || "";
    profileForm.birthday = currentUser.value.birthday || "";
    profileForm.gender = currentUser.value.gender || "male";
  }
};

// Update profile
const onUpdateProfile = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    loading.value = true;

    const res: UserResult = await updateCurrentUser({
      firstName: profileForm.firstName,
      lastName: profileForm.lastName,
      phone: profileForm.phone,
      address: profileForm.address,
      birthday: profileForm.birthday,
      gender: profileForm.gender
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    // Update user store
    await userStore.getUserInfo();
    message(res.message || $t("Profile updated successfully"), {
      type: "success"
    });
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Failed to update profile"),
      { type: "error" }
    );
  } finally {
    loading.value = false;
  }
};

// Update password
const onUpdatePassword = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    await formEl.validate();
    passwordLoading.value = true;

    const res: UserResult = await updateCurrentUserPassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword,
      passwordConfirmation: passwordForm.confirmPassword
    });

    if (!res.success) {
      message(res.message, { type: "error" });
      return;
    }

    // Clear password form
    passwordForm.currentPassword = "";
    passwordForm.newPassword = "";
    passwordForm.confirmPassword = "";

    message(res.message || $t("Password updated successfully"), {
      type: "success"
    });
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Failed to update password"),
      { type: "error" }
    );
  } finally {
    passwordLoading.value = false;
  }
};

// Handle avatar upload
const handleAvatarUpload = async (file: any) => {
  try {
    avatarLoading.value = true;
    const formData = new FormData();
    formData.append("avatar", file.raw);

    const res: UserResult = await updateCurrentUserAvatar(formData);

    if (!res.success) {
      message(res.message, { type: "error" });
      return false;
    }

    // Update user store
    await userStore.getUserInfo();
    message(res.message || $t("Avatar updated successfully"), {
      type: "success"
    });
    return true;
  } catch (error: any) {
    message(
      error?.response?.data?.message ||
        error?.message ||
        $t("Failed to update avatar"),
      { type: "error" }
    );
    return false;
  } finally {
    avatarLoading.value = false;
  }
};

const beforeAvatarUpload = (file: any) => {
  const isImage = file.type.startsWith("image/");
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isImage) {
    message($t("Avatar must be an image"), { type: "error" });
    return false;
  }
  if (!isLt2M) {
    message($t("Avatar size must be less than 2MB"), { type: "error" });
    return false;
  }
  return true;
};

onMounted(() => {
  loadUserData();
});
</script>

<template>
  <div class="profile-container">
    <!-- Main Content -->
    <div class="p-6">
      <div class="w-full max-w-6xl mx-auto">
        <!-- Profile Header -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <div class="flex items-center space-x-6">
            <!-- Avatar Upload -->
            <div class="relative">
              <el-avatar
                :size="120"
                :src="currentUser?.avatar"
                :icon="useRenderIcon(User)"
                class="border-4 border-white shadow-lg"
              />
              <el-upload
                ref="avatarUploadRef"
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :http-request="handleAvatarUpload"
                accept="image/*"
                class="absolute bottom-0 right-0"
              >
                <el-button
                  :icon="useRenderIcon(Camera)"
                  circle
                  size="small"
                  type="primary"
                  :loading="avatarLoading"
                  class="shadow-lg"
                />
              </el-upload>
            </div>

            <!-- User Info -->
            <div class="flex-1">
              <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {{ fullName }}
              </h1>
              <p class="text-gray-600 dark:text-gray-300 mb-1">
                @{{ currentUser?.username }}
              </p>
              <p class="text-gray-500 dark:text-gray-400">
                {{ currentUser?.email }}
              </p>
              <div class="flex items-center mt-3 space-x-4">
                <el-tag
                  v-if="currentUser?.isVerified"
                  type="success"
                  size="small"
                >
                  {{ $t("Verified") }}
                </el-tag>
                <el-tag
                  :type="
                    currentUser?.status === 'active' ? 'success' : 'warning'
                  "
                  size="small"
                >
                  {{ $t(currentUser?.status || "Unknown") }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- Profile Forms -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Profile Information Form -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2
              class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center"
            >
              <IconifyIconOnline :icon="'ri:user-settings-line'" class="mr-2" />
              {{ $t("Profile Information") }}
            </h2>

            <el-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              size="large"
              label-position="top"
            >
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Motion :delay="100">
                  <el-form-item :label="$t('First Name')" prop="firstName">
                    <el-input
                      v-model="profileForm.firstName"
                      :placeholder="$t('Enter first name')"
                      :prefix-icon="useRenderIcon(User)"
                      clearable
                    />
                  </el-form-item>
                </Motion>

                <Motion :delay="150">
                  <el-form-item :label="$t('Last Name')" prop="lastName">
                    <el-input
                      v-model="profileForm.lastName"
                      :placeholder="$t('Enter last name')"
                      :prefix-icon="useRenderIcon(User)"
                      clearable
                    />
                  </el-form-item>
                </Motion>
              </div>

              <Motion :delay="200">
                <el-form-item :label="$t('Email')" prop="email">
                  <el-input
                    v-model="profileForm.email"
                    :placeholder="$t('Enter email')"
                    :prefix-icon="useRenderIcon(Mail)"
                    disabled
                    readonly
                  />
                </el-form-item>
              </Motion>

              <Motion :delay="250">
                <el-form-item :label="$t('Phone')" prop="phone">
                  <el-input
                    v-model="profileForm.phone"
                    :placeholder="$t('Enter phone number')"
                    :prefix-icon="useRenderIcon(Phone)"
                    clearable
                  />
                </el-form-item>
              </Motion>

              <Motion :delay="300">
                <el-form-item :label="$t('Address')" prop="address">
                  <el-input
                    v-model="profileForm.address"
                    :placeholder="$t('Enter address')"
                    :prefix-icon="useRenderIcon(MapPin)"
                    type="textarea"
                    :rows="3"
                  />
                </el-form-item>
              </Motion>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Motion :delay="350">
                  <el-form-item :label="$t('Birthday')" prop="birthday">
                    <el-date-picker
                      v-model="profileForm.birthday"
                      type="date"
                      :placeholder="$t('Select birthday')"
                      :prefix-icon="useRenderIcon(Calendar)"
                      class="w-full"
                      format="YYYY-MM-DD"
                      value-format="YYYY-MM-DD"
                    />
                  </el-form-item>
                </Motion>

                <Motion :delay="400">
                  <el-form-item :label="$t('Gender')" prop="gender">
                    <el-select
                      v-model="profileForm.gender"
                      :placeholder="$t('Select gender')"
                      class="w-full"
                    >
                      <el-option :label="$t('Male')" value="male" />
                      <el-option :label="$t('Female')" value="female" />
                      <el-option :label="$t('Other')" value="other" />
                    </el-select>
                  </el-form-item>
                </Motion>
              </div>

              <Motion :delay="450">
                <el-button
                  type="primary"
                  size="large"
                  :loading="loading"
                  :disabled="disabled"
                  class="w-full"
                  @click="onUpdateProfile(profileFormRef)"
                >
                  <IconifyIconOnline :icon="Save" class="mr-2" />
                  {{ $t("Update Profile") }}
                </el-button>
              </Motion>
            </el-form>
          </div>

          <!-- Password Change Form -->
          <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2
              class="text-xl font-semibold text-gray-900 dark:text-white mb-6 flex items-center"
            >
              <IconifyIconOnline :icon="'ri:lock-password-line'" class="mr-2" />
              {{ $t("Change Password") }}
            </h2>

            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="profileRules"
              size="large"
              label-position="top"
            >
              <Motion :delay="100">
                <el-form-item
                  :label="$t('Current Password')"
                  prop="currentPassword"
                >
                  <el-input
                    v-model="passwordForm.currentPassword"
                    type="password"
                    :placeholder="$t('Enter current password')"
                    :prefix-icon="useRenderIcon(Lock)"
                    show-password
                    clearable
                  />
                </el-form-item>
              </Motion>

              <Motion :delay="150">
                <el-form-item :label="$t('New Password')" prop="newPassword">
                  <el-input
                    v-model="passwordForm.newPassword"
                    type="password"
                    :placeholder="$t('Enter new password')"
                    :prefix-icon="useRenderIcon(Lock)"
                    show-password
                    clearable
                  />
                </el-form-item>
              </Motion>

              <Motion :delay="200">
                <el-form-item
                  :label="$t('Confirm New Password')"
                  prop="confirmPassword"
                >
                  <el-input
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    :placeholder="$t('Confirm new password')"
                    :prefix-icon="useRenderIcon(Lock)"
                    show-password
                    clearable
                  />
                </el-form-item>
              </Motion>

              <Motion :delay="250">
                <el-button
                  type="danger"
                  size="large"
                  :loading="passwordLoading"
                  :disabled="disabled"
                  class="w-full"
                  @click="onUpdatePassword(passwordFormRef)"
                >
                  <IconifyIconOnline :icon="'ri:key-2-line'" class="mr-2" />
                  {{ $t("Update Password") }}
                </el-button>
              </Motion>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.profile-container {
  min-height: calc(100vh - 200px);
  background: var(--el-bg-color-page);
}

.flex-c {
  display: flex;
  align-items: center;
}
</style>

<style lang="scss" scoped>
:deep(.el-input-group__append, .el-input-group__prepend) {
  padding: 0;
}

:deep(.el-upload) {
  border: none;
  border-radius: 50%;
}

:deep(.el-avatar) {
  border: 4px solid #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.el-card) {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

:deep(.el-form-item__label) {
  font-weight: 600;
  color: var(--el-text-color-primary);
}
</style>
