<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, h, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import TinyEditor from "@/components/TinyEditor/index.vue";
import { Language } from "@/api/types/language";
import { useLanguageStore } from "@/store/modules/language";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
  disabled?: boolean;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const formRef = ref();
const loading = ref(false);
const languages = ref([] as Language[]);
const currentLocale = ref("vi");
const translations = ref({} as any);

const formColumns = computed<PlusColumn[]>(() => [
  {
    label: computed(() => $t("Language")),
    prop: "locale",
    valueType: "select",
    required: true,
    fieldProps: {
      placeholder: $t("Select language"),
      clearable: false,
      modelValue: currentLocale.value,
      "onUpdate:modelValue": (value: string) => {
        currentLocale.value = value;
      },
      disabled: props.disabled,
      onChange: (value: string) => {
        currentLocale.value = value;
      }
    },
    options: languages.value.map(lang => ({
      label: lang.native,
      value: lang.locale,
      render: () =>
        h("div", { class: "flex items-center" }, [
          lang.flag
            ? h("img", {
                src: lang.flag,
                class: "w-5 h-4 mr-2",
                alt: lang.name
              })
            : null,
          h("span", {}, lang.name)
        ])
    })),
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Title")),
    prop: "title",
    valueType: "input",
    required: true,
    fieldProps: {
      placeholder: "",
      disabled: props.disabled
    },
    rules: [
      {
        required: true,
        message: $t("Please enter title"),
        trigger: ["blur", "change"]
      }
    ],
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Slug")),
    prop: "slug",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      disabled: props.disabled
    },
    rules: [
      {
        pattern: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
        message: $t("Slug format is invalid"),
        trigger: ["blur", "change"]
      }
    ],
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Excerpt")),
    prop: "excerpt",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 3,
      maxlength: 300,
      showWordLimit: true,
      disabled: props.disabled
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Content")),
    prop: "content",
    valueType: "textarea",
    required: true,
    fieldProps: {
      placeholder: "",
      disabled: props.disabled
    },
    rules: [
      {
        required: true,
        message: $t("Please enter content"),
        trigger: ["blur", "change"]
      }
    ],
    renderField: (value: string, onChange: (value: string) => void) => {
      return h(TinyEditor, {
        modelValue: value,
        "onUpdate:modelValue": (newValue: string) => {
          onChange(newValue);
        },
        height: 500,
        style: { width: "100%" },
        disabled: props.disabled
      });
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Meta title")),
    prop: "metaTitle",
    valueType: "input",
    fieldProps: {
      placeholder: "",
      disabled: props.disabled
    },
    colProps: { span: 24 }
  },
  {
    label: computed(() => $t("Meta description")),
    prop: "metaDescription",
    valueType: "textarea",
    fieldProps: {
      placeholder: "",
      rows: 3,
      maxlength: 160,
      showWordLimit: true,
      disabled: props.disabled
    },
    colProps: { span: 24 }
  }
]);

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;

  await formRef.value.formInstance.validate(async (valid: boolean) => {
    if (valid) {
      try {
        loading.value = true;

        // Prepare translations data
        values.translations = {
          [currentLocale.value]: {
            id: translations.value[currentLocale.value]?.id || null,
            pageId:
              translations.value[currentLocale.value]?.pageId ??
              props.values.id,
            title: values.title,
            slug: values.slug,
            content: values.content,
            metaTitle: values.metaTitle,
            metaDescription: values.metaDescription,
            excerpt: values.excerpt
          }
        };
        emit("submit", values);
      } finally {
        loading.value = false;
      }
    }
  });
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

watch(
  () => props.visible,
  newValue => {
    if (newValue) {
      languages.value = useLanguageStore().languages;
      currentLocale.value = useLanguageStore().currentLocale;
      // eslint-disable-next-line vue/no-mutating-props
      props.values.locale = currentLocale.value;
      // @ts-ignore
      props.values.translations?.forEach((item: any) => {
        translations.value[item.locale] = item;
      });
    }
  }
);

watch(
  () => currentLocale.value,
  newLocale => {
    if (!formRef.value?.formInstance || !props.values.translations) return;

    currentLocale.value = newLocale;

    let translation = translations.value[newLocale];

    translations.value[newLocale] = {
      id: translations.value[newLocale]?.id || null,
      pageId: translations.value[newLocale]?.pageId ?? props.values.id,
      title: translation?.title || translations.value["vi"]?.title,
      slug: translation?.slug || translations.value["vi"]?.slug,
      content: translation?.content || translations.value["vi"]?.content,
      metaTitle: translation?.metaTitle || translations.value["vi"]?.metaTitle,
      metaDescription:
        translation?.metaDescription ||
        translations.value["vi"]?.metaDescription,
      excerpt: translation?.excerpt || translations.value["vi"]?.excerpt
    };

    const values = {
      ...translations.value[newLocale],
      locale: newLocale,
      status: props.values.status,
      translations: translations.value,
      id: props.values.id
    };
    emit("update:values", values);
  }
);

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="70%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 16 },
      disabled: disabled
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Translate Page") }}
        </span>
        <el-tag type="success" size="small" class="ml-2">
          {{ $t("Translating") }}
        </el-tag>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :disabled="disabled"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>

<style lang="scss">
.tiny-editor-container {
  .el-drawer__body {
    overflow: visible;
  }

  .tox-tinymce-aux {
    z-index: 9999;
  }
}

.custom-group-header {
  @apply flex items-center;
}
</style>
