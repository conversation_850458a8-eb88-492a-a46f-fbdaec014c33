import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getCategories = (params?: any) => {
  return http.request<Result>("get", "/api/auth/blog/categories", { params });
};

export const getCategoriesDropdown = () => {
  return http.request<Result>("get", "/api/auth/blog/categories/dropdown");
};

export const getCategoryById = (id: number) => {
  return http.request<Result>("get", `/api/auth/blog/categories/${id}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createCategory = (data: any) => {
  return http.request<Result>("post", "/api/auth/blog/categories", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateCategoryById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/auth/blog/categories/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteCategory = (id: number) => {
  return http.request<Result>("delete", `/api/auth/blog/categories/${id}/delete`);
};

export const bulkDeleteCategories = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/blog/categories/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteCategoryPermanent = (id: number) => {
  return http.request<Result>("delete", `/api/auth/blog/categories/${id}/force`);
};

export const bulkDeleteCategoriesPermanent = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/blog/categories/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreCategory = (id: number) => {
  return http.request<Result>("post", `/api/auth/blog/categories/${id}/restore`);
};

export const bulkRestoreCategories = (data: { ids: number[] }) => {
  return http.request<Result>("post", "/api/auth/blog/categories/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};
