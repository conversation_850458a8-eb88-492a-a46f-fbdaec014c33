import { reactive, ref, onMounted } from "vue";
import { $t } from "@/plugins/i18n";
import type { FieldValues } from "plus-pro-components";
import { message } from "@/utils/message";
import { ElMessageBox } from "element-plus";

import { useConvertKeyToCamel, useConvertKeyToSnake } from "@/utils/helpers";
import type { FormItemProps, PageFilterProps } from "./type";
import {
  getPages,
  getPagesDropdown,
  createPage,
  updatePageById,
  bulkDeletePages,
  deletePage,
  deletePagePermanent,
  bulkDeletePagesPermanent,
  restorePage,
  bulkRestorePages
} from "./auth-api";

export function usePageHook() {
  /*
   ***************************
   *   Data/State Management
   ***************************
   */
  const loading = ref(false);
  const filterRef = ref<PageFilterProps>({ isTrashed: "no" });
  const pagination = reactive({
    total: 0,
    pageSize: 20,
    currentPage: 1,
    pageSizes: [10, 20, 50, 100, 200],
    background: true,
    layout: "total, sizes, prev, pager, next, jumper"
  });
  const multipleSelection = ref([]);
  const records = ref([]);
  const sort = ref({ sortBy: "createdAt", sortOrder: "asc" });
  const filterVisible = ref(false);
  const drawerVisible = ref(false);
  const drawerValues = ref<FieldValues>({
    status: "active"
  });
  const pageFormRef = ref();

  /*
   ***************************
   *   API Data Fetching
   ***************************
   */
  const fnGetPages = async () => {
    loading.value = true;
    try {
      const response = await getPages({
        ...filterRef.value,
        order: `${sort.value.sortBy}:${sort.value.sortOrder}`,
        page: pagination.currentPage,
        limit: pagination.pageSize
      });
      records.value = useConvertKeyToCamel(response.data);
      pagination.total = response.total;
    } catch (e) {
      console.error("Get Pages error:", e);
      message(e.response?.data?.message || e?.message || $t("Get failed"), {
        type: "error"
      });
    } finally {
      loading.value = false;
    }
  };

  /*
   ***************************
   *   Table Event Handlers
   ***************************
   */
  const fnHandleSelectionChange = (val: any) => {
    multipleSelection.value = val;
  };

  const fnHandleSortChange = ({ prop, order }) => {
    sort.value.sortBy = prop;
    sort.value.sortOrder = order === "ascending" ? "asc" : "desc";
    fnGetPages();
  };

  const fnHandlePageChange = () => {
    fnGetPages();
  };

  const fnHandleSizeChange = (size: number) => {
    pagination.pageSize = size;
    pagination.currentPage = 1;
    fnGetPages();
  };

  /*
   ***************************
   *   UI Action Handlers - Soft Delete
   ***************************
   */
  const handleDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move this item to trash?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      const response = await deletePage(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetPages();
      } else {
        message(response?.message || $t("Delete failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Delete failed"),
          { type: "error" }
        );
      }
    }
  };

  const handleBulkDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to move selected items to trash?"),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "warning"
        }
      );

      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDeletePages({ ids });
      if (response.success) {
        message(response.message || $t("Bulk delete successful"), {
          type: "success"
        });
        await fnGetPages();
      } else {
        message(response?.message || $t("Bulk delete failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Bulk delete failed"),
          { type: "error" }
        );
      }
    }
  };

  /*
   ***************************
   *   UI Action Handlers - Permanent Delete
   ***************************
   */
  const handlePermanentDelete = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete this item? This action cannot be undone."),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );

      const response = await deletePagePermanent(id);
      if (response.success) {
        message(response.message || $t("Delete successful"), {
          type: "success"
        });
        await fnGetPages();
      } else {
        message(response?.message || $t("Delete failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Delete failed"),
          { type: "error" }
        );
      }
    }
  };

  const handleBulkPermanentDelete = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to delete"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to permanently delete selected items? This action cannot be undone."),
        $t("Warning"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "error"
        }
      );

      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkDeletePagesPermanent({ ids });
      if (response.success) {
        message(response.message || $t("Bulk delete successful"), {
          type: "success"
        });
        await fnGetPages();
      } else {
        message(response?.message || $t("Bulk delete failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Bulk delete failed"),
          { type: "error" }
        );
      }
    }
  };

  /*
   ***************************
   *   UI Action Handlers - Restore
   ***************************
   */
  const handleRestore = async (id: number) => {
    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore this item?"),
        $t("Confirmation"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      const response = await restorePage(id);
      if (response.success) {
        message(response.message || $t("Restore successful"), {
          type: "success"
        });
        await fnGetPages();
      } else {
        message(response?.message || $t("Restore failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Restore failed"),
          { type: "error" }
        );
      }
    }
  };

  const handleBulkRestore = async () => {
    if (multipleSelection.value.length === 0) {
      message($t("Please select items to restore"), { type: "warning" });
      return;
    }

    try {
      await ElMessageBox.confirm(
        $t("Are you sure you want to restore selected items?"),
        $t("Confirmation"),
        {
          confirmButtonText: $t("OK"),
          cancelButtonText: $t("Cancel"),
          type: "info"
        }
      );

      const ids = multipleSelection.value.map((item: any) => item.id);
      const response = await bulkRestorePages({ ids });
      if (response.success) {
        message(response.message || $t("Bulk restore successful"), {
          type: "success"
        });
        await fnGetPages();
      } else {
        message(response?.message || $t("Bulk restore failed"), {
          type: "error"
        });
      }
    } catch (error) {
      if (error !== "cancel") {
        message(
          error.response?.data?.message || error?.message || $t("Bulk restore failed"),
          { type: "error" }
        );
      }
    }
  };

  /*
   ***************************
   *   Form Handlers
   ***************************
   */
  const handleSubmit = async (values: FieldValues) => {
    if (values.id != null) {
      const response = await updatePageById(Number(values.id), values);
      if (response.success) {
        message(response.message || $t("Update successful"), {
          type: "success"
        });
        await fnGetPages();
        drawerVisible.value = false;
        drawerValues.value = { status: "active" };
        pageFormRef.value?.resetForm();
      } else {
        message(response?.message || $t("Update failed"), {
          type: "error"
        });
      }
      return;
    }

    const response = await createPage(values);
    if (response.success) {
      message(response.message || $t("Create successful"), {
        type: "success"
      });
      await fnGetPages();
      drawerVisible.value = false;
      drawerValues.value = { status: "active" };
      pageFormRef.value?.resetForm();
    } else {
      message(response?.message || $t("Create failed"), {
        type: "error"
      });
    }
  };

  const handleFilter = async (values: PageFilterProps) => {
    filterRef.value = values;
    pagination.currentPage = 1;
    await fnGetPages();
  };

  /*
   ***************************
   *   Return Hook Interface
   ***************************
   */
  return {
    // Data/State
    loading,
    filterRef,
    pagination,
    records,
    multipleSelection,
    // Event handlers
    handleBulkDelete,
    handleDelete,
    fnGetPages,
    fnHandlePageChange,
    fnHandleSelectionChange,
    fnHandleSortChange,
    fnHandleSizeChange,
    // Form related
    filterVisible,
    drawerVisible,
    drawerValues,
    pageFormRef,
    handleSubmit,
    handleFilter,
    handleBulkPermanentDelete,
    handleBulkRestore,
    handlePermanentDelete,
    handleRestore
  };
}
