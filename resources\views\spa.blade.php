<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ config('app.name', 'ProCMS Admin') }}</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
    
    <!-- Include your built SPA assets -->
    @if (file_exists(public_path('dist/index.html')))
        @php
            $manifest = file_get_contents(public_path('dist/index.html'));
            // Extract CSS and JS files from the built index.html
            preg_match_all('/<link[^>]*href="([^"]*\.css)"[^>]*>/', $manifest, $cssMatches);
            preg_match_all('/<script[^>]*src="([^"]*\.js)"[^>]*>/', $manifest, $jsMatches);
        @endphp
        
        @foreach($cssMatches[1] ?? [] as $cssFile)
            <link rel="stylesheet" href="{{ asset('dist/' . ltrim($cssFile, '/')) }}">
        @endforeach
    @endif
</head>
<body>
    <div id="app"></div>
    
    @if (file_exists(public_path('dist/index.html')))
        @foreach($jsMatches[1] ?? [] as $jsFile)
            <script src="{{ asset('dist/' . ltrim($jsFile, '/')) }}"></script>
        @endforeach
    @else
        <div style="padding: 20px; text-align: center;">
            <h2>SPA not built yet</h2>
            <p>Please run <code>npm run build</code> to build the SPA.</p>
        </div>
    @endif
</body>
</html>
