import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getKnowledgeBases = (params?: any) => {
  return http.request<Result>("get", "/api/auth/knowledge-bases", { params });
};

export const getKnowledgeBaseById = (id: string) => {
  return http.request<Result>("get", `/api/auth/knowledge-bases/${id}`);
};

export const getKnowledgeBasesDropdown = () => {
  return http.request<Result>("get", "/api/auth/knowledge-bases/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createKnowledgeBase = (data: any) => {
  return http.request<Result>("post", "/api/auth/knowledge-bases", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateKnowledgeBaseById = (id: string, data: any) => {
  return http.request<Result>("put", `/api/auth/knowledge-bases/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteKnowledgeBase = (id: string) => {
  return http.request<Result>(
    "delete",
    `/api/auth/knowledge-bases/${id}/delete`
  );
};

export const bulkDeleteKnowledgeBases = (data: { ids: string[] }) => {
  return http.request<Result>(
    "delete",
    "/api/auth/knowledge-bases/bulk/delete",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteKnowledgeBasePermanent = (id: string) => {
  return http.request<Result>(
    "delete",
    `/api/auth/knowledge-bases/${id}/force`
  );
};

export const bulkDeleteKnowledgeBasesPermanent = (data: { ids: string[] }) => {
  return http.request<Result>(
    "delete",
    "/api/auth/knowledge-bases/bulk/force",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreKnowledgeBase = (id: string) => {
  return http.request<Result>("put", `/api/auth/knowledge-bases/${id}/restore`);
};

export const bulkRestoreKnowledgeBases = (data: { ids: string[] }) => {
  return http.request<Result>("put", "/api/auth/knowledge-bases/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};

export const bulkRetrainKnowledgeBases = (data: { ids: string[] }) => {
  return http.request<Result>("post", "/api/auth/knowledge-bases/retrain", {
    data: useConvertKeyToSnake(data)
  });
};
