<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>{{ config('app.name', 'ProCMS Admin') }}</title>
    
    <!-- Favicon -->
    <link rel="icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Load the built SPA assets from dist folder -->
    @if (file_exists(public_path('dist/index.html')))
        @php
            $indexContent = file_get_contents(public_path('dist/index.html'));
            
            // Extract CSS links
            preg_match_all('/<link[^>]*rel="stylesheet"[^>]*href="([^"]*)"[^>]*>/i', $indexContent, $cssMatches);
            
            // Extract JS scripts
            preg_match_all('/<script[^>]*src="([^"]*)"[^>]*><\/script>/i', $indexContent, $jsMatches);
            
            // Extract other meta tags and links
            preg_match_all('/<link[^>]*rel="(?!stylesheet)[^"]*"[^>]*>/i', $indexContent, $otherLinks);
        @endphp
        
        <!-- Include CSS files -->
        @if(isset($cssMatches[1]))
            @foreach($cssMatches[1] as $cssFile)
                <link rel="stylesheet" href="{{ asset(ltrim($cssFile, '/')) }}">
            @endforeach
        @endif
        
        <!-- Include other link tags (preload, etc.) -->
        @if(isset($otherLinks[0]))
            @foreach($otherLinks[0] as $linkTag)
                {!! $linkTag !!}
            @endforeach
        @endif
    @endif
</head>
<body>
    <div id="app"></div>
    
    @if (file_exists(public_path('dist/index.html')))
        <!-- Include JS files -->
        @if(isset($jsMatches[1]))
            @foreach($jsMatches[1] as $jsFile)
                <script src="{{ asset(ltrim($jsFile, '/')) }}"></script>
            @endforeach
        @endif
    @else
        <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif;">
            <h2>Application Not Built</h2>
            <p>The SPA has not been built yet. Please run:</p>
            <code style="background: #f5f5f5; padding: 10px; display: block; margin: 10px 0;">npm run build</code>
            <p>Then refresh this page.</p>
        </div>
    @endif
</body>
</html>
