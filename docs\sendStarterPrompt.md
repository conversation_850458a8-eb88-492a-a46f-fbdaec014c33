# sendStarterPrompt Function Documentation

## 📋 **Tổng quan**

`sendStarterPrompt` là một function trong chat system cho phép người dùng gửi các câu hỏi/prompt được định sẵn (starter messages) một cách nhanh chóng mà không cần phải gõ lại.

## 🎯 **Chức năng**

- **Gửi starter prompt**: Tự động điền và gửi tin nhắn từ các gợi ý có sẵn
- **Tích hợp với chat flow**: Sử dụng cùng logic gửi tin nhắn như `sendMessage`
- **UX tối ưu**: Giúp người dùng bắt đầu cuộc hội thoại nhanh chóng

## 🔧 **Implementation**

### **Location**: `src/views/chat/utils/hook.ts`

```typescript
// Send starter prompt as a message
const sendStarterPrompt = async (prompt: string) => {
  if (!prompt.trim()) return;
  
  // Set the prompt as new message and send it
  newMessage.value = prompt;
  await sendMessage();
};
```

### **Parameters**:
- `prompt: string` - Nội dung starter message cần gửi

### **Return**: `Promise<void>`

## 🎨 **Cách sử dụng**

### **1. Trong ChatMain.vue**

```vue
<template>
  <div class="flex flex-wrap justify-center gap-2 max-w-lg">
    <el-button
      v-for="(prompt, index) in agent.starterMessages"
      :key="`prompt-${index}`"
      round
      @click="sendStarterPrompt(prompt)"
    >
      {{ prompt }}
    </el-button>
  </div>
</template>

<script setup>
const { sendStarterPrompt } = props.chatBot;
</script>
```

### **2. Trong component khác**

```typescript
import { useChatBot } from '@/views/chat/utils/hook';

const { sendStarterPrompt } = useChatBot();

// Gửi starter prompt
await sendStarterPrompt("What is the tuition fee?");
```

## 🔄 **Flow hoạt động**

1. **User click** vào starter button
2. **sendStarterPrompt** được gọi với prompt text
3. **Validate** prompt không rỗng
4. **Set** `newMessage.value = prompt`
5. **Call** `sendMessage()` để gửi tin nhắn
6. **Follow** normal chat flow (create conversation nếu cần, gửi API, etc.)

## 📊 **Starter Messages**

### **Nguồn gốc**: 
Starter messages được tạo từ:
- **AI Generation**: Sử dụng `generateStarters()` trong BotForm
- **Manual Input**: Người dùng tự nhập trong bot configuration

### **Cấu trúc**:
```typescript
interface Bot {
  starterMessages: string[]; // Array of starter prompts
  // ... other properties
}
```

### **Ví dụ**:
```javascript
starterMessages: [
  "What is the tuition fee?",
  "How to apply for admission?", 
  "What are the available programs?",
  "Tell me about campus facilities"
]
```

## 🎯 **Tích hợp với Bot Configuration**

### **Generate Starters** (BotForm.vue):
```typescript
const generateStarters = async () => {
  const result = await callGeneralPrompts({
    type: "starting_message",
    system_prompt: drawerValues.systemPrompt
  });
  
  if (result) {
    const starters = JSON.parse(result);
    drawerValues.starterMessages = starters.slice(0, 4);
  }
};
```

### **Edit Starters** (BotChatInterface.vue):
```typescript
const updateStarter = (index: number, value: string) => {
  const newStarters = [...(props.starterMessages || [])];
  newStarters[index] = value;
  emit("update:starterMessages", newStarters);
};
```

## ✅ **Best Practices**

1. **Validation**: Luôn kiểm tra prompt không rỗng
2. **Error Handling**: Sử dụng try-catch cho async operations
3. **UX**: Disable buttons khi đang typing hoặc loading
4. **Accessibility**: Thêm proper labels và keyboard navigation

## 🔗 **Related Functions**

- `sendMessage()` - Core message sending function
- `generateStarters()` - AI generation of starter prompts
- `updateStarter()` - Edit starter messages in bot config
- `triggerAgentResponse()` - Handle AI response after sending

## 📝 **Notes**

- Function này đơn giản hóa việc gửi tin nhắn từ starter prompts
- Tái sử dụng logic của `sendMessage()` để đảm bảo consistency
- Tự động handle conversation creation nếu cần thiết
- Tích hợp với real-time chat system qua WebSocket/Soketi
