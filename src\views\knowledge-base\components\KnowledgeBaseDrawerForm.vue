<script setup lang="ts">
import "plus-pro-components/es/components/drawer-form/style/css";
import {
  type FieldValues,
  type PlusColumn,
  PlusDrawerForm
} from "plus-pro-components";
import { $t } from "@/plugins/i18n";
import { computed, h, onMounted, ref, watch } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { getToken } from "@/utils/auth";
import {
  ElIcon,
  ElMessage,
  ElUpload,
  UploadFile,
  UploadFiles
} from "element-plus";
import { UploadFilled } from "@element-plus/icons-vue";

const props = defineProps<{
  visible: boolean;
  values: FieldValues;
}>();

const emit = defineEmits<{
  (e: "update:visible", value: boolean): void;
  (e: "update:values", value: FieldValues): void;
  (e: "submit", values: FieldValues): void;
}>();

const loading = ref(false);

const formRef = ref();

const formColumns: PlusColumn[] = [
  {
    label: computed(() => $t("KnowledgeBase Name")),
    prop: "name",
    valueType: "input",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input Knowledge-base name"),
        trigger: ["blur"]
      },
      {
        min: 2,
        max: 100,
        message: $t("Length must be between 2 and 100 characters"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: ""
    }
  },
  {
    label: computed(() => $t("Type")),
    prop: "type",
    valueType: "select",
    required: true,
    options: [
      { label: $t("File"), value: "file" },
      { label: $t("Text"), value: "text" }
    ],
    fieldProps: {
      placeholder: "",
      clearable: false,
      // @ts-ignore
      disabled: computed(() => props.values.uuid)
    },
    onChange: (value: string) => {
      if (value === "text") {
        emit("update:values", {
          ...props.values,
          files: [],
          storagePath: null
        });
      }
    }
  },
  {
    hideInForm: computed(
      () => props.values.type == "file" || props.values.uuid != null
    ),
    label: computed(() => $t("Content")),
    prop: "content",
    valueType: "textarea",
    required: true,
    rules: [
      {
        required: true,
        message: $t("Please input Knowledge-base content"),
        trigger: ["blur"]
      }
    ],
    fieldProps: {
      placeholder: "",
      rows: 6
    }
  },
  {
    hideInForm: computed(
      () => props.values.type != "file" || props.values.uuid != null
    ),
    label: computed(() => $t("Upload Files")),
    prop: "files",
    required: true,
    rules: [
      {
        validator: (_rule: any, _value: any, callback: any) => {
          if (
            props.values.type === "file" &&
            !props.values.storagePath &&
            (!Array.isArray(props.values.files) ||
              props.values.files.length === 0)
          ) {
            callback(new Error($t("Please upload at least one file")));
          } else {
            callback();
          }
        },
        trigger: "change"
      }
    ],
    renderField: model => {
      return h(
        ElUpload,
        {
          class: "upload-demo !w-full",
          drag: true,
          action: "/api/auth/knowledge-bases/files/upload",
          multiple: false,
          limit: 1,
          "auto-upload": true,
          headers: {
            Authorization: `Bearer ${getToken().accessToken ?? getToken()}`,
            "X-Requested-With": "XMLHttpRequest"
          },
          onExceed: handleUploadExceed,
          onSuccess: handleUploadSuccess,
          onError: handleUploadError,
          "on-change": (_uploadFile: UploadFile, uploadFiles: UploadFiles) => {
            console.log("Upload change:", uploadFiles, model);
          }
        },
        {
          default: () => [
            h(
              ElIcon,
              { class: "el-icon--upload" },
              { default: () => h(UploadFilled) }
            ),
            h("div", { class: "el-upload__text" }, [
              $t("Drop file here or "),
              h("em", $t("click to upload"))
            ])
          ],
          tip: () => h("div", { class: "el-upload__tip" }, [])
        }
      );
    }
  }
];

const handleUploadExceed = () => {
  ElMessage.warning(
    $t("You can only upload one file. Please remove the existing file first.")
  );
};

const handleUploadSuccess = (response: any, _file: any) => {
  if (response.data && response.data.storage_path) {
    emit("update:values", {
      ...props.values,
      storagePath: response.data.storage_path,
      files: [response.data]
    });
  }
};

const handleUploadError = (error: any) => {
  console.error("Upload error:", error);
};

const handleSubmit = async (values: FieldValues) => {
  if (!formRef.value?.formInstance) return;
  const valid = await formRef.value.formInstance.validate();
  if (!valid) return;

  try {
    loading.value = true;

    const submitValues = { ...values };
    if (submitValues.type === "file" && submitValues.storagePath) {
      delete submitValues.files;
    }
    emit("submit", submitValues);
  } finally {
    setTimeout(() => {
      loading.value = false;
    }, 3000);
  }
};

const resetForm = () => {
  if (formRef.value?.formInstance) {
    formRef.value.formInstance.resetFields();
  }
};

defineExpose({
  resetForm
});
</script>

<template>
  <PlusDrawerForm
    ref="formRef"
    :visible="visible"
    :model-value="values"
    size="40%"
    :closeOnClickModal="true"
    :closeOnPressEscape="true"
    :showClose="true"
    :destroyOnClose="true"
    :form="{
      columns: formColumns,
      footerAlign: 'center',
      hasFooter: false,
      labelSuffix: '',
      labelPosition: 'top',
      labelWidth: 'auto',
      labelAlign: 'left',
      requireAsteriskPosition: 'right',
      rowProps: { gutter: 10 }
    }"
    @update:visible="val => emit('update:visible', val)"
    @update:model-value="val => emit('update:values', val)"
    @close="resetForm"
  >
    <template #drawer-header>
      <div class="custom-group-header">
        <span class="font-semibold">
          {{ $t("Information Form") }}
        </span>
      </div>
    </template>
    <template #drawer-footer>
      <div class="custom-footer">
        <el-button plain @click="emit('update:visible', false)">
          {{ $t("Cancel") }}
        </el-button>
        <el-button
          plain
          type="primary"
          :loading="loading"
          :icon="useRenderIcon('ri:save-2-line')"
          @click="handleSubmit(values)"
        >
          {{ $t("Save") }}
        </el-button>
      </div>
    </template>
  </PlusDrawerForm>
</template>
