const Layout = () => import("@/layout/index.vue");

export default {
  path: "/ai-bots/management",
  name: "<PERSON><PERSON>",
  component: Layout,
  redirect: "/ai-bots",
  meta: {
    icon: "ri:robot-2-line",
    title: "AI Bots",
    rank: 4
  },
  children: [
    {
      path: "/ai-bots",
      name: "BotIndex",
      component: () => import("@/views/bot/index.vue"),
      meta: {
        icon: "ri:robot-2-line",
        title: "AI Bots",
        showLink: true,
        roles: ["super-admin", "admin"]
      }
    },
    {
      path: "/ai-bots/groups",
      name: "BotGroup",
      component: () => import("@/views/bot/index.vue"),
      meta: {
        icon: "ri:group-line",
        title: "Bot Group",
        showLink: false
      }
    },
    {
      path: "/ai-bots/share",
      name: "BotShare",
      component: () => import("@/views/bot/index.vue"),
      meta: {
        icon: "ri:share-line",
        title: "Bot Share With Me",
        showLink: false
      }
    }
  ]
} satisfies RouteConfigsTable;
