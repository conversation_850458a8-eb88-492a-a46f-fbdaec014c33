# Troubleshooting Guide

Common issues and solutions for the ProcMS Chatbot Widget.

## Quick Diagnostics

### Enable Debug Mode

```javascript
// Enable debug logging
window.PROCMS_DEBUG = true;

// Check widget status
console.log('Widget loaded:', typeof ProcmsChatbotWidget !== 'undefined');
console.log('Widget config:', widget?.getConfig());
console.log('Widget mode:', widget?.getMode());
```

### Check Browser Console

1. Open browser developer tools (F12)
2. Go to Console tab
3. Look for error messages starting with "ProcMS" or "Chatbot"
4. Check Network tab for failed API requests

### Verify Configuration

```javascript
// Test configuration
const config = {
  botUuid: 'your-bot-uuid',
  apiKey: 'pk_live_your_api_key'
};

// Validate required fields
if (!config.botUuid) console.error('Missing botUuid');
if (!config.apiKey) console.error('Missing apiKey');
if (!config.apiKey.startsWith('pk_')) console.error('Invalid API key format');
```

## Common Issues

### Widget Not Loading

#### Symptoms
- Widget container remains empty
- No error messages in console
- Loading indicator shows indefinitely

#### Possible Causes & Solutions

**1. Invalid Configuration**
```javascript
// Check configuration
const widget = new ProcmsChatbotWidget({
  botUuid: 'bot-123', // Verify this is correct
  apiKey: 'pk_live_xxx' // Verify API key format and permissions
});

// Test API connectivity
fetch(`/api/public/bots/${botUuid}/config`, {
  headers: { 'Authorization': `Bearer ${apiKey}` }
}).then(response => {
  console.log('API Status:', response.status);
  if (!response.ok) {
    console.error('API Error:', response.statusText);
  }
});
```

**2. Container Element Not Found**
```javascript
// Ensure container exists before mounting
const container = document.getElementById('chatbot-container');
if (!container) {
  console.error('Container element not found');
  return;
}

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', () => {
  const widget = new ProcmsChatbotWidget(config);
  widget.mount('#chatbot-container');
});
```

**3. Network/CORS Issues**
```javascript
// Check CORS headers
fetch('/api/public/bots/test', { mode: 'cors' })
  .then(response => console.log('CORS OK'))
  .catch(error => console.error('CORS Error:', error));

// Test with different API endpoint
const widget = new ProcmsChatbotWidget({
  ...config,
  apiEndpoint: 'https://api.procms.com' // Try different endpoint
});
```

**4. Content Security Policy (CSP)**
```html
<!-- Add to your CSP if needed -->
<meta http-equiv="Content-Security-Policy" 
      content="script-src 'self' 'unsafe-inline' https://cdn.procms.com; 
               connect-src 'self' https://api.procms.com;
               style-src 'self' 'unsafe-inline';">
```

### CSS Conflicts

#### Symptoms
- Widget appears broken or unstyled
- Layout issues
- Text not visible or wrong colors

#### Solutions

**1. Increase CSS Specificity**
```css
/* Use higher specificity */
.procms-chatbot-widget.procms-chatbot-widget {
  font-family: 'Your Font', sans-serif !important;
  color: #333 !important;
}

/* Or use CSS variables */
.procms-chatbot-widget {
  --procms-font-family: 'Your Font', sans-serif;
  --procms-text-primary: #333;
}
```

**2. Force Iframe Mode**
```javascript
// Use iframe for complete isolation
const widget = new ProcmsChatbotWidget({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  mode: 'iframe' // Forces iframe isolation
});
```

**3. Reset Inherited Styles**
```css
.procms-chatbot-widget {
  all: initial;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  /* Re-apply needed styles */
}
```

### JavaScript Conflicts

#### Symptoms
- Widget functionality broken
- Console errors about undefined functions
- Event handlers not working

#### Solutions

**1. Check for Global Variable Conflicts**
```javascript
// Check for conflicts
console.log('Conflicting globals:', {
  Vue: typeof Vue,
  React: typeof React,
  jQuery: typeof $,
  ProcmsChatbotWidget: typeof ProcmsChatbotWidget
});

// Use namespace if needed
window.MyApp = window.MyApp || {};
window.MyApp.chatbot = new ProcmsChatbotWidget(config);
```

**2. Use Iframe Mode for Isolation**
```javascript
const widget = new ProcmsChatbotWidget({
  ...config,
  mode: 'iframe' // Complete JavaScript isolation
});
```

**3. Load Widget After Other Scripts**
```html
<!-- Load your scripts first -->
<script src="your-app.js"></script>

<!-- Load widget last -->
<script src="https://cdn.procms.com/widget/procms-chatbot.umd.js"></script>
```

### API Errors

#### 401 Unauthorized

**Cause:** Invalid or expired API key

**Solution:**
```javascript
// Check API key format
if (!apiKey.startsWith('pk_live_') && !apiKey.startsWith('pk_test_')) {
  console.error('Invalid API key format');
}

// Test API key
fetch('/api/auth/verify', {
  headers: { 'Authorization': `Bearer ${apiKey}` }
}).then(response => {
  if (response.status === 401) {
    console.error('API key is invalid or expired');
  }
});
```

#### 404 Not Found

**Cause:** Bot UUID doesn't exist or is inactive

**Solution:**
```javascript
// Verify bot exists
fetch(`/api/public/bots/${botUuid}`)
  .then(response => {
    if (response.status === 404) {
      console.error('Bot not found or inactive');
    }
  });

// Check bot status in admin panel
```

#### 429 Rate Limited

**Cause:** Too many requests

**Solution:**
```javascript
// Implement retry with backoff
const widget = new ProcmsChatbotWidget({
  ...config,
  retryConfig: {
    maxRetries: 3,
    retryDelay: 1000,
    backoffMultiplier: 2
  }
});
```

#### 500 Server Error

**Cause:** Server-side issue

**Solution:**
```javascript
// Implement error handling
widget.on('error', (error) => {
  if (error.status === 500) {
    console.error('Server error, please try again later');
    // Show user-friendly message
    showErrorMessage('Service temporarily unavailable');
  }
});
```

### Mobile Issues

#### Widget Too Small on Mobile

```css
@media (max-width: 768px) {
  .procms-chatbot-widget {
    width: 100vw !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    border-radius: 0 !important;
  }
}
```

#### Touch Events Not Working

```javascript
// Ensure touch events are enabled
const widget = new ProcmsChatbotWidget({
  ...config,
  touchEnabled: true,
  swipeGestures: true
});
```

#### Zoom Issues on iOS

```html
<!-- Prevent zoom on input focus -->
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
```

```css
/* Ensure minimum font size */
.procms-input-area__input {
  font-size: 16px !important; /* Prevents zoom on iOS */
}
```

### Performance Issues

#### Slow Loading

**1. Use CDN**
```html
<!-- Use CDN for faster loading -->
<script src="https://cdn.procms.com/widget/procms-chatbot.umd.js"></script>
```

**2. Lazy Load Widget**
```javascript
// Load widget only when needed
function loadChatbot() {
  if (!window.chatbotLoaded) {
    const script = document.createElement('script');
    script.src = 'https://cdn.procms.com/widget/procms-chatbot.umd.js';
    script.onload = () => {
      window.chatbotLoaded = true;
      initializeChatbot();
    };
    document.head.appendChild(script);
  }
}

// Load on user interaction
document.getElementById('chat-button').addEventListener('click', loadChatbot);
```

**3. Optimize Configuration**
```javascript
const widget = new ProcmsChatbotWidget({
  ...config,
  preloadMessages: false, // Don't preload message history
  enableAnimations: false, // Disable animations for better performance
  maxMessages: 50 // Limit message history
});
```

#### Memory Leaks

**1. Proper Cleanup**
```javascript
// Always cleanup when done
window.addEventListener('beforeunload', () => {
  if (widget) {
    widget.destroy();
  }
});

// In React/Vue components
useEffect(() => {
  return () => {
    if (widget) {
      widget.unmount();
    }
  };
}, []);
```

**2. Remove Event Listeners**
```javascript
// Remove specific listeners
const messageHandler = (msg) => console.log(msg);
widget.on('message', messageHandler);

// Later...
widget.off('message', messageHandler);

// Or remove all listeners
widget.off('message');
```

### Browser Compatibility

#### Internet Explorer Support

```javascript
// Check for IE and show fallback
if (navigator.userAgent.indexOf('MSIE') !== -1 || 
    navigator.userAgent.indexOf('Trident') !== -1) {
  
  // Show fallback message
  document.getElementById('chatbot-container').innerHTML = 
    '<p>Please use a modern browser for the best experience.</p>';
  return;
}
```

#### Safari Issues

```javascript
// Safari-specific fixes
const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);

if (isSafari) {
  // Use iframe mode for better compatibility
  config.mode = 'iframe';
  
  // Disable certain features
  config.enableAnimations = false;
}
```

## Debugging Tools

### Widget Inspector

```javascript
// Add to console for debugging
window.debugWidget = function(widget) {
  console.log('Widget Debug Info:', {
    isMounted: widget.isMounted(),
    mode: widget.getMode(),
    config: widget.getConfig(),
    botConfig: widget.getBotConfig(),
    element: widget.getElement?.(),
    events: widget.getEventListeners?.()
  });
};

// Usage
debugWidget(widget);
```

### Network Monitor

```javascript
// Monitor API calls
const originalFetch = window.fetch;
window.fetch = function(...args) {
  console.log('API Call:', args[0]);
  return originalFetch.apply(this, args)
    .then(response => {
      console.log('API Response:', response.status, response.statusText);
      return response;
    })
    .catch(error => {
      console.error('API Error:', error);
      throw error;
    });
};
```

### Performance Monitor

```javascript
// Monitor widget performance
const performanceObserver = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    if (entry.name.includes('procms')) {
      console.log('Performance:', entry.name, entry.duration + 'ms');
    }
  });
});

performanceObserver.observe({ entryTypes: ['measure', 'navigation'] });
```

## Getting Help

### Before Contacting Support

1. **Check this troubleshooting guide**
2. **Enable debug mode** and check console
3. **Test with minimal configuration**
4. **Try iframe mode** to isolate issues
5. **Test on different browsers/devices**

### Information to Include

When contacting support, please provide:

- Widget version
- Browser and version
- Operating system
- Complete error messages from console
- Minimal code example that reproduces the issue
- Network tab screenshots (if API related)
- Widget configuration (remove sensitive data)

### Support Channels

- 📧 **Email**: <EMAIL>
- 💬 **Community Forum**: https://community.procms.com
- 📖 **Documentation**: https://docs.procms.com
- 🐛 **Bug Reports**: https://github.com/procms/widget/issues

### Emergency Issues

For critical production issues:
- Use priority support email: <EMAIL>
- Include "URGENT" in subject line
- Provide detailed impact assessment
- Include steps already taken to resolve

## Prevention Tips

1. **Test thoroughly** before deploying to production
2. **Monitor error rates** and performance metrics
3. **Keep widget updated** to latest stable version
4. **Implement proper error handling** in your application
5. **Use staging environment** for testing changes
6. **Document your configuration** for team reference
7. **Set up monitoring** for widget availability and performance
