const Layout = () => import("@/layout/index.vue");

export default {
  path: "/ai-knowledge/management",
  name: "KnowledgeBase",
  component: Layout,
  redirect: "/ai-knowledge",
  meta: {
    icon: "ri:book-line",
    title: "AI Knowledge",
    rank: 6
  },
  children: [
    {
      path: "/ai-knowledge",
      name: "KnowledgeBaseIndex",
      component: () => import("@/views/knowledge-base/index.vue"),
      meta: {
        icon: "ri:book-line",
        title: "AI Knowledge",
        showLink: true,
        auths: ["knowledge-base.read"]
      }
    }
  ]
} satisfies RouteConfigsTable;
