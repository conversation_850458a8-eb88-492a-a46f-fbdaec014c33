import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getProviders = (params?: any) => {
  return http.request<Result>("get", "/api/auth/providers", { params });
};

export const getProviderById = (id: number) => {
  return http.request<Result>("get", `/api/auth/providers/${id}`);
};

export const getProvidersDropdown = () => {
  return http.request<Result>("get", "/api/auth/providers/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createProvider = (data: any) => {
  return http.request<Result>("post", "/api/auth/providers", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateProviderById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/auth/providers/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteProvider = (id: number) => {
  return http.request<Result>("delete", `/api/auth/providers/${id}/delete`);
};

export const bulkDeleteProviders = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/providers/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteProviderPermanent = (id: number) => {
  return http.request<Result>("delete", `/api/auth/providers/${id}/force`);
};

export const bulkDeleteProvidersPermanent = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/providers/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreProvider = (id: number) => {
  return http.request<Result>("put", `/api/auth/providers/${id}/restore`);
};

export const bulkRestoreProviders = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/providers/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};
