import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getPosts = (params?: any) => {
  return http.request<Result>("get", "/api/auth/blog/posts", { params });
};

export const getPostsDropdown = () => {
  return http.request<Result>("get", "/api/auth/blog/posts/dropdown");
};

export const getPostById = (id: number) => {
  return http.request<Result>("get", `/api/auth/blog/posts/${id}`);
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createPost = (data: any) => {
  return http.request<Result>("post", "/api/auth/blog/posts", {
    data: useConvertKeyToSnake(data)
  });
};

export const updatePostById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/auth/blog/posts/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deletePost = (id: number) => {
  return http.request<Result>("delete", `/api/auth/blog/posts/${id}/delete`);
};

export const bulkDeletePosts = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/blog/posts/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deletePostPermanent = (id: number) => {
  return http.request<Result>("delete", `/api/auth/blog/posts/${id}/force`);
};

export const bulkDeletePostsPermanent = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/blog/posts/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restorePost = (id: number) => {
  return http.request<Result>("post", `/api/auth/blog/posts/${id}/restore`);
};

export const bulkRestorePosts = (data: { ids: number[] }) => {
  return http.request<Result>("post", "/api/auth/blog/posts/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};
