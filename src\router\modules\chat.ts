const Layout = () => import("@/layout/index.vue");

export default {
  path: "/ai-chat/management",
  name: "<PERSON><PERSON>",
  redirect: "/ai-chat",
  component: Layout,
  meta: {
    icon: "ri:chat-3-line",
    title: "AI Chat",
    rank: 5
  },
  children: [
    {
      path: "/ai-chat",
      name: "ChatIndex",
      component: () => import("@/views/chat/index.vue"),
      meta: {
        icon: "ri:chat-3-line",
        title: "AI Chat",
        showLink: true
      }
    }
  ]
} satisfies RouteConfigsTable;
