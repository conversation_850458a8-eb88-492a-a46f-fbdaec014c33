import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getPages = (params?: any) => {
  return http.request<Result>("get", "/api/auth/pages", { params });
};

export const getPageById = (id: number) => {
  return http.request<Result>("get", `/api/auth/pages/${id}`);
};

export const getPagesDropdown = () => {
  return http.request<Result>("get", "/api/auth/pages/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createPage = (data: any) => {
  return http.request<Result>("post", "/api/auth/pages", {
    data: useConvertKeyToSnake(data)
  });
};

export const updatePageById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/auth/pages/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deletePage = (id: number) => {
  return http.request<Result>("delete", `/api/auth/pages/${id}/delete`);
};

export const bulkDeletePages = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/pages/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deletePagePermanent = (id: number) => {
  return http.request<Result>("delete", `/api/auth/pages/${id}/force`);
};

export const bulkDeletePagesPermanent = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/pages/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restorePage = (id: number) => {
  return http.request<Result>("post", `/api/auth/pages/${id}/restore`);
};

export const bulkRestorePages = (data: { ids: number[] }) => {
  return http.request<Result>("post", "/api/auth/pages/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};
