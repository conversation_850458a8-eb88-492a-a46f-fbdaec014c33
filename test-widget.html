<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProcMS Widget Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .widget-container {
            width: 400px;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ProcMS Chatbot Widget - Test Page</h1>
        <p>Testing the widget functionality and integration.</p>

        <!-- Test 1: Basic Widget Loading -->
        <div class="test-section">
            <h3>Test 1: Basic Widget Loading</h3>
            <p>Test if the widget library loads correctly.</p>
            
            <button class="btn" onclick="testLibraryLoading()">Test Library Loading</button>
            <div id="status-1" class="status" style="display: none;"></div>
        </div>

        <!-- Test 2: Widget Configuration -->
        <div class="test-section">
            <h3>Test 2: Widget Configuration</h3>
            <p>Test widget configuration and validation.</p>
            
            <button class="btn" onclick="testConfiguration()">Test Configuration</button>
            <div id="status-2" class="status" style="display: none;"></div>
        </div>

        <!-- Test 3: Mock Widget Creation -->
        <div class="test-section">
            <h3>Test 3: Mock Widget Creation</h3>
            <p>Test widget creation with mock data.</p>
            
            <div id="mock-widget-container" class="widget-container"></div>
            <button class="btn" onclick="testMockWidget()">Create Mock Widget</button>
            <button class="btn" onclick="clearMockWidget()">Clear Widget</button>
            <div id="status-3" class="status" style="display: none;"></div>
        </div>

        <!-- Test 4: Theme System -->
        <div class="test-section">
            <h3>Test 4: Theme System</h3>
            <p>Test theme switching and customization.</p>
            
            <div>
                <button class="btn" onclick="testTheme('light')">Light Theme</button>
                <button class="btn" onclick="testTheme('dark')">Dark Theme</button>
                <button class="btn" onclick="testCustomTheme()">Custom Theme</button>
            </div>
            <div id="theme-demo" style="
                width: 300px; 
                height: 200px; 
                border: 1px solid #ddd; 
                border-radius: 8px; 
                margin: 10px 0;
                padding: 20px;
                background: var(--procms-widget-bg, #f9fafb);
                color: var(--procms-text-primary, #111827);
                transition: all 0.3s ease;
            ">
                <div style="font-weight: 600; margin-bottom: 10px;">Theme Demo</div>
                <div style="font-size: 14px; color: var(--procms-text-secondary, #6b7280);">
                    This demonstrates theme switching
                </div>
            </div>
            <div id="status-4" class="status" style="display: none;"></div>
        </div>

        <!-- Test 5: Error Handling -->
        <div class="test-section">
            <h3>Test 5: Error Handling</h3>
            <p>Test error handling and recovery.</p>
            
            <button class="btn" onclick="testErrorHandling()">Test Error Handling</button>
            <div id="status-5" class="status" style="display: none;"></div>
        </div>

        <!-- Debug Log -->
        <div class="test-section">
            <h3>Debug Log</h3>
            <div id="debug-log" class="log">
                Debug information will appear here...
            </div>
            <button class="btn" onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <!-- Load Widget Library -->
    <link rel="stylesheet" href="dist/widget/procms-chatbot.css">
    <script src="dist/widget/procms-chatbot.umd.js"></script>
    <script>
        // Debug logging
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEl = document.getElementById('debug-log');
            logEl.innerHTML += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logEl.scrollTop = logEl.scrollHeight;
            console.log(`[Widget Test] ${message}`);
        }

        function showStatus(testId, message, type = 'info') {
            const statusEl = document.getElementById(`status-${testId}`);
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
            log(`Test ${testId}: ${message}`, type);
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = 'Debug log cleared...\n';
        }

        // Test 1: Library Loading
        function testLibraryLoading() {
            log('Testing library loading...');
            
            try {
                // Check if classes are available
                const hasWidget = typeof ProcmsChatbotWidget !== 'undefined';
                const hasChatbot = typeof ProcmsChatbot !== 'undefined';
                
                if (hasWidget && hasChatbot) {
                    showStatus(1, 'Widget library loaded successfully!', 'success');
                    log('Available classes: ProcmsChatbotWidget, ProcmsChatbot');
                } else {
                    showStatus(1, 'Widget library not found. Check if script is loaded.', 'error');
                    log('Missing classes - Widget: ' + hasWidget + ', Chatbot: ' + hasChatbot);
                }
            } catch (error) {
                showStatus(1, `Error: ${error.message}`, 'error');
                log('Library loading error: ' + error.message, 'error');
            }
        }

        // Test 2: Configuration
        function testConfiguration() {
            log('Testing configuration...');
            
            try {
                // Test valid configuration
                const validConfig = {
                    botUuid: 'test-bot-uuid-123',
                    apiKey: 'pk_test_1234567890123456789012345678901234567890'
                };
                
                log('Testing valid configuration...');
                const widget = new ProcmsChatbotWidget(validConfig);
                log('Valid configuration accepted');
                
                // Test invalid configuration
                try {
                    log('Testing invalid configuration...');
                    const invalidWidget = new ProcmsChatbotWidget({});
                    showStatus(2, 'Configuration validation failed!', 'error');
                } catch (configError) {
                    log('Invalid configuration properly rejected: ' + configError.message);
                    showStatus(2, 'Configuration validation working correctly!', 'success');
                }
                
            } catch (error) {
                showStatus(2, `Error: ${error.message}`, 'error');
                log('Configuration test error: ' + error.message, 'error');
            }
        }

        // Test 3: Mock Widget
        function testMockWidget() {
            log('Testing mock widget creation...');
            
            try {
                const container = document.getElementById('mock-widget-container');
                
                // Create mock widget HTML
                container.innerHTML = `
                    <div class="procms-chatbot-widget" data-procms-theme="light" style="
                        height: 100%;
                        display: flex;
                        flex-direction: column;
                        background: var(--procms-widget-bg, #f9fafb);
                        border-radius: 8px;
                        overflow: hidden;
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                    ">
                        <!-- Header -->
                        <div class="procms-widget-header" style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                            height: 60px;
                            padding: 0 16px;
                            background: var(--procms-primary, #3b82f6);
                            color: white;
                        ">
                            <div style="display: flex; align-items: center; gap: 12px;">
                                <div style="
                                    width: 32px;
                                    height: 32px;
                                    border-radius: 50%;
                                    background: rgba(255,255,255,0.2);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                ">🤖</div>
                                <div>
                                    <div style="font-weight: 600; font-size: 16px;">Test Bot</div>
                                    <div style="font-size: 12px; opacity: 0.8;">Online</div>
                                </div>
                            </div>
                            <button onclick="clearMockWidget()" style="
                                background: none;
                                border: none;
                                color: white;
                                cursor: pointer;
                                padding: 4px;
                                border-radius: 4px;
                            ">×</button>
                        </div>
                        
                        <!-- Messages -->
                        <div style="
                            flex: 1;
                            padding: 16px;
                            overflow-y: auto;
                            background: var(--procms-widget-bg, #f9fafb);
                        ">
                            <div style="
                                display: flex;
                                gap: 8px;
                                margin-bottom: 16px;
                            ">
                                <div style="
                                    width: 32px;
                                    height: 32px;
                                    border-radius: 50%;
                                    background: var(--procms-primary, #3b82f6);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    color: white;
                                    font-size: 14px;
                                ">🤖</div>
                                <div style="
                                    background: white;
                                    padding: 12px 16px;
                                    border-radius: 18px;
                                    border-bottom-left-radius: 4px;
                                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                                    max-width: 80%;
                                ">
                                    <div>Hello! This is a mock widget for testing. How can I help you?</div>
                                    <div style="font-size: 11px; color: #666; margin-top: 4px;">
                                        ${new Date().toLocaleTimeString()}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Input -->
                        <div style="
                            padding: 16px;
                            background: white;
                            border-top: 1px solid #e5e7eb;
                        ">
                            <div style="display: flex; gap: 8px; align-items: center;">
                                <input type="text" placeholder="Type a message..." style="
                                    flex: 1;
                                    padding: 12px 16px;
                                    border: 1px solid #e5e7eb;
                                    border-radius: 24px;
                                    outline: none;
                                    font-size: 14px;
                                " />
                                <button style="
                                    width: 40px;
                                    height: 40px;
                                    border-radius: 50%;
                                    background: var(--procms-primary, #3b82f6);
                                    color: white;
                                    border: none;
                                    cursor: pointer;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                ">→</button>
                            </div>
                        </div>
                    </div>
                `;
                
                showStatus(3, 'Mock widget created successfully!', 'success');
                log('Mock widget HTML generated and displayed');
                
            } catch (error) {
                showStatus(3, `Error: ${error.message}`, 'error');
                log('Mock widget creation error: ' + error.message, 'error');
            }
        }

        function clearMockWidget() {
            document.getElementById('mock-widget-container').innerHTML = '';
            showStatus(3, 'Mock widget cleared.', 'info');
            log('Mock widget cleared');
        }

        // Test 4: Theme System
        function testTheme(theme) {
            log(`Testing ${theme} theme...`);
            
            try {
                const demo = document.getElementById('theme-demo');
                demo.setAttribute('data-procms-theme', theme);
                
                // Apply theme-specific CSS variables
                if (theme === 'dark') {
                    demo.style.setProperty('--procms-widget-bg', '#1f2937');
                    demo.style.setProperty('--procms-text-primary', '#f3f4f6');
                    demo.style.setProperty('--procms-text-secondary', '#d1d5db');
                } else {
                    demo.style.setProperty('--procms-widget-bg', '#f9fafb');
                    demo.style.setProperty('--procms-text-primary', '#111827');
                    demo.style.setProperty('--procms-text-secondary', '#6b7280');
                }
                
                showStatus(4, `${theme} theme applied successfully!`, 'success');
                log(`Theme switched to ${theme}`);
                
            } catch (error) {
                showStatus(4, `Error: ${error.message}`, 'error');
                log('Theme test error: ' + error.message, 'error');
            }
        }

        function testCustomTheme() {
            log('Testing custom theme...');
            
            try {
                const demo = document.getElementById('theme-demo');
                
                // Apply custom theme
                demo.style.setProperty('--procms-widget-bg', '#ff6b6b');
                demo.style.setProperty('--procms-text-primary', '#ffffff');
                demo.style.setProperty('--procms-text-secondary', '#ffcccc');
                
                showStatus(4, 'Custom theme applied successfully!', 'success');
                log('Custom theme (red) applied');
                
            } catch (error) {
                showStatus(4, `Error: ${error.message}`, 'error');
                log('Custom theme test error: ' + error.message, 'error');
            }
        }

        // Test 5: Error Handling
        function testErrorHandling() {
            log('Testing error handling...');
            
            try {
                // Test various error scenarios
                const errors = [
                    'Invalid configuration',
                    'Network timeout',
                    'API authentication failed',
                    'Bot not found'
                ];
                
                errors.forEach((errorMsg, index) => {
                    setTimeout(() => {
                        log(`Simulated error ${index + 1}: ${errorMsg}`, 'error');
                    }, index * 500);
                });
                
                showStatus(5, 'Error handling test completed. Check debug log.', 'success');
                
            } catch (error) {
                showStatus(5, `Error: ${error.message}`, 'error');
                log('Error handling test error: ' + error.message, 'error');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', () => {
            log('Test page loaded');
            log('Checking for widget library...');
            
            // Check if widget library is available
            if (typeof ProcmsChatbotWidget === 'undefined') {
                log('Widget library not found. This is expected if not built yet.', 'info');
                
                // Create mock classes for testing
                window.ProcmsChatbotWidget = class MockWidget {
                    constructor(config) {
                        if (!config.botUuid) throw new Error('Bot UUID is required');
                        if (!config.apiKey) throw new Error('API Key is required');
                        this.config = config;
                        log('Mock ProcmsChatbotWidget created');
                    }
                };
                
                window.ProcmsChatbot = class MockChatbot {
                    static async create(config, selector) {
                        log('Mock ProcmsChatbot.create called');
                        return new ProcmsChatbotWidget(config);
                    }
                };
                
                log('Mock classes created for testing');
            } else {
                log('Widget library found!');
            }
        });
    </script>
</body>
</html>
