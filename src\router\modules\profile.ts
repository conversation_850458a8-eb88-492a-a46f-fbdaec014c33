const Layout = () => import("@/layout/index.vue");

export default {
  path: "/profile",
  name: "Profile",
  component: Layout,
  redirect: "/profile/index",
  meta: {
    icon: "ri:user-settings-line",
    title: "Profile",
    rank: 999,
    showLink: false
  },
  children: [
    {
      path: "/profile/index",
      name: "ProfileIndex",
      component: () => import("@/views/auth/profile/index.vue"),
      meta: {
        icon: "ri:user-settings-line",
        title: "My Profile",
        showLink: false,
        auths: ["profile.read"]
      }
    }
  ]
} satisfies RouteConfigsTable;
