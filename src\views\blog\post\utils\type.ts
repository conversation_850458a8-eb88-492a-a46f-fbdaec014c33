export type Post = {
  id: number;
  title: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  categoryId?: number | null;
  authorId?: number | null;
  featuredImage?: string;
  status?: "draft" | "published" | "archived";
  publishedAt?: string;
  sortOrder?: number;
  // SEO fields
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
  // Translation fields
  locale?: string;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string;
};

export type FormItemProps = {
  id?: number | null;
  title?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  categoryId?: number | null;
  authorId?: number | null;
  featuredImage?: string;
  status?: "draft" | "published" | "archived";
  publishedAt?: string;
  sortOrder?: number;
  // SEO fields
  metaTitle?: string;
  metaDescription?: string;
  metaKeywords?: string;
  // Translation fields
  locale?: string;
};

export type PostFilterProps = {
  title?: string;
  slug?: string;
  categoryId?: number | null;
  authorId?: number | null;
  status?: "draft" | "published" | "archived";
  isTrashed?: "yes" | "no";
  [key: string]: any;
};
