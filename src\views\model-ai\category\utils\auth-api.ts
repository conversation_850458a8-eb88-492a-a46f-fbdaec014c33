import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getCategories = (params?: any) => {
  return http.request<Result>("get", "/api/auth/model-categories", { params });
};

export const getCategoryById = (id: number) => {
  return http.request<Result>("get", `/api/auth/model-categories/${id}`);
};

export const getCategoriesDropdown = () => {
  return http.request<Result>("get", "/api/auth/model-categories/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createCategory = (data: any) => {
  return http.request<Result>("post", "/api/auth/model-categories", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateCategoryById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/auth/model-categories/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteCategory = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/auth/model-categories/${id}/delete`
  );
};

export const bulkDeleteCategories = (data: { ids: number[] }) => {
  return http.request<Result>(
    "delete",
    "/api/auth/model-categories/bulk/delete",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteCategoryPermanent = (id: number) => {
  return http.request<Result>(
    "delete",
    `/api/auth/model-categories/${id}/force`
  );
};

export const bulkDeleteCategoriesPermanent = (data: { ids: number[] }) => {
  return http.request<Result>(
    "delete",
    "/api/auth/model-categories/bulk/force",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreCategory = (id: number) => {
  return http.request<Result>(
    "put",
    `/api/auth/model-categories/${id}/restore`
  );
};

export const bulkRestoreCategories = (data: { ids: number[] }) => {
  return http.request<Result>(
    "put",
    "/api/auth/model-categories/bulk/restore",
    {
      data: useConvertKeyToSnake(data)
    }
  );
};
