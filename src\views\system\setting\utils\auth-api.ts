import { http } from "@/utils/http";
import { useConvertKeyToSnake } from "@/utils/helpers";
import type { Result } from "@/utils/response";

/*
 ***************************
 *   Read Operations (GET)
 ***************************
 */
export const getRoles = (params?: any) => {
  return http.request<Result>("get", "/api/auth/roles", { params });
};

export const getPermissionsDropdown = (params?: any) => {
  return http.request<Result>("get", "/api/auth/permissions", { params });
};

export const getRoleById = (id: number) => {
  return http.request<Result>("get", `/api/auth/roles/${id}`);
};

export const getRolesDropdown = () => {
  return http.request<Result>("get", "/api/auth/roles/dropdown");
};

/*
 ***************************
 *   Create & Update Operations
 ***************************
 */
export const createRole = (data: any) => {
  return http.request<Result>("post", "/api/auth/roles", {
    data: useConvertKeyToSnake(data)
  });
};

export const updateRoleById = (id: number, data: any) => {
  return http.request<Result>("put", `/api/auth/roles/${id}`, {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Soft Delete Operations
 ***************************
 */
export const deleteRole = (id: number) => {
  return http.request<Result>("delete", `/api/auth/roles/${id}/delete`);
};

export const bulkDeleteRoles = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/roles/bulk/delete", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Permanent Delete Operations
 ***************************
 */
export const deleteRolePermanent = (id: number) => {
  return http.request<Result>("delete", `/api/auth/roles/${id}/force`);
};

export const bulkDeleteRolesPermanent = (data: { ids: number[] }) => {
  return http.request<Result>("delete", "/api/auth/roles/bulk/force", {
    data: useConvertKeyToSnake(data)
  });
};

/*
 ***************************
 *   Restore Operations
 ***************************
 */
export const restoreRole = (id: number) => {
  return http.request<Result>("put", `/api/auth/roles/${id}/restore`);
};

export const bulkRestoreRoles = (data: { ids: number[] }) => {
  return http.request<Result>("put", "/api/auth/roles/bulk/restore", {
    data: useConvertKeyToSnake(data)
  });
};
