# API Reference

Complete API reference for the ProcMS Chatbot Widget.

## Classes

### ProcmsChatbotWidget

Main widget class for creating and managing chatbot instances.

#### Constructor

```typescript
new ProcmsChatbotWidget(config: WidgetConfig)
```

**Parameters:**
- `config` (WidgetConfig): Widget configuration object

**Example:**
```javascript
const widget = new ProcmsChatbotWidget({
  botUuid: 'your-bot-uuid',
  apiKey: 'pk_live_your_api_key',
  theme: 'light'
});
```

#### Methods

##### mount(selector: string | HTMLElement): Promise<void>

Mounts the widget to a DOM element.

**Parameters:**
- `selector`: CSS selector string or HTMLElement

**Returns:** Promise that resolves when widget is mounted

**Example:**
```javascript
await widget.mount('#chatbot-container');
// or
await widget.mount(document.getElementById('container'));
```

##### unmount(): void

Unmounts the widget and cleans up resources.

**Example:**
```javascript
widget.unmount();
```

##### destroy(): void

Completely destroys the widget instance and removes all event listeners.

**Example:**
```javascript
widget.destroy();
```

##### setTheme(theme: ThemeMode): void

Changes the widget theme.

**Parameters:**
- `theme`: 'light' | 'dark' | 'auto'

**Example:**
```javascript
widget.setTheme('dark');
```

##### setCustomTheme(themeConfig: ThemeConfig): void

Applies a custom theme configuration.

**Parameters:**
- `themeConfig`: Custom theme configuration object

**Example:**
```javascript
widget.setCustomTheme({
  primaryColor: '#ff6b6b',
  backgroundColor: '#f8f9fa',
  textPrimary: '#2d3436'
});
```

##### updateConfig(config: Partial<WidgetConfig>): void

Updates widget configuration.

**Parameters:**
- `config`: Partial configuration object

**Example:**
```javascript
widget.updateConfig({
  theme: 'dark',
  showHeader: false
});
```

##### on(event: string, callback: Function): void

Adds an event listener.

**Parameters:**
- `event`: Event name
- `callback`: Event handler function

**Example:**
```javascript
widget.on('message', (message) => {
  console.log('New message:', message);
});
```

##### off(event: string, callback?: Function): void

Removes event listener(s).

**Parameters:**
- `event`: Event name
- `callback`: Optional specific callback to remove

**Example:**
```javascript
widget.off('message', specificHandler);
// or remove all listeners for event
widget.off('message');
```

##### once(event: string, callback: Function): void

Adds a one-time event listener.

**Parameters:**
- `event`: Event name
- `callback`: Event handler function

**Example:**
```javascript
widget.once('ready', () => {
  console.log('Widget is ready!');
});
```

#### Properties

##### isMounted(): boolean

Returns whether the widget is currently mounted.

**Example:**
```javascript
if (widget.isMounted()) {
  console.log('Widget is mounted');
}
```

##### getMode(): 'widget' | 'iframe'

Returns the current rendering mode.

**Example:**
```javascript
const mode = widget.getMode();
console.log('Current mode:', mode);
```

##### getBotConfig(): BotConfig | null

Returns the loaded bot configuration.

**Example:**
```javascript
const botConfig = widget.getBotConfig();
if (botConfig) {
  console.log('Bot name:', botConfig.name);
}
```

##### getConfig(): WidgetConfig

Returns the current widget configuration.

**Example:**
```javascript
const config = widget.getConfig();
console.log('Current theme:', config.theme);
```

### ProcmsChatbot (Static Class)

Static utility class for creating widgets and utility functions.

#### Static Methods

##### create(config: WidgetConfig, selector: string | HTMLElement): Promise<ProcmsChatbotWidget>

Creates and mounts a widget in one call.

**Parameters:**
- `config`: Widget configuration
- `selector`: Target element selector or HTMLElement

**Returns:** Promise resolving to widget instance

**Example:**
```javascript
const widget = await ProcmsChatbot.create({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx'
}, '#container');
```

##### createFloatingWidget(config: WidgetConfig): Promise<ProcmsChatbotWidget>

Creates a floating widget that appears over page content.

**Parameters:**
- `config`: Widget configuration with position options

**Returns:** Promise resolving to widget instance

**Example:**
```javascript
const widget = await ProcmsChatbot.createFloatingWidget({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  position: 'bottom-right'
});
```

##### generateIframeCode(config: WidgetConfig): string

Generates HTML iframe embed code.

**Parameters:**
- `config`: Widget configuration

**Returns:** HTML string for iframe

**Example:**
```javascript
const iframeCode = ProcmsChatbot.generateIframeCode({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  width: 400,
  height: 600
});
```

##### init(config: WidgetConfig): Promise<ProcmsChatbotWidget>

Auto-initializes widget based on configuration and page context.

**Parameters:**
- `config`: Widget configuration

**Returns:** Promise resolving to widget instance

**Example:**
```javascript
const widget = await ProcmsChatbot.init({
  botUuid: 'bot-123',
  apiKey: 'pk_live_xxx',
  mode: 'auto'
});
```

##### detectMode(config: WidgetConfig): 'widget' | 'iframe'

Detects the best rendering mode for the current environment.

**Parameters:**
- `config`: Widget configuration

**Returns:** Recommended mode

**Example:**
```javascript
const mode = ProcmsChatbot.detectMode(config);
console.log('Recommended mode:', mode);
```

##### getDetectionReport(config: WidgetConfig): DetectionReport

Gets detailed compatibility report.

**Parameters:**
- `config`: Widget configuration

**Returns:** Detection report object

**Example:**
```javascript
const report = ProcmsChatbot.getDetectionReport(config);
console.log('Conflicts:', report.conflicts);
console.log('Recommended mode:', report.recommendedMode);
```

## Interfaces

### WidgetConfig

Main configuration interface for the widget.

```typescript
interface WidgetConfig {
  // Required
  botUuid: string;
  apiKey: string;
  
  // Optional
  userId?: string;
  theme?: 'light' | 'dark' | 'auto';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left' | 'center';
  autoOpen?: boolean;
  showHeader?: boolean;
  showAvatar?: boolean;
  width?: number;
  height?: number;
  mode?: 'widget' | 'iframe' | 'auto';
  customCSS?: string;
  metadata?: Record<string, any>;
  
  // Event handlers
  onReady?: () => void;
  onMessage?: (message: ChatMessage) => void;
  onError?: (error: Error) => void;
  onConversationStarted?: (conversationId: string) => void;
  onConversationEnded?: (conversationId: string) => void;
  onBotLoaded?: (botConfig: BotConfig) => void;
  onMounted?: (info: MountInfo) => void;
  onUnmounted?: () => void;
}
```

### BotConfig

Bot configuration loaded from the API.

```typescript
interface BotConfig {
  uuid: string;
  name: string;
  description?: string;
  logoUrl?: string;
  greetingMessage?: string;
  starterMessages?: string[];
  theme?: {
    primaryColor?: string;
    backgroundColor?: string;
    textColor?: string;
    borderRadius?: string;
    fontFamily?: string;
    customVariables?: Record<string, string>;
  };
  settings?: {
    allowFileUpload?: boolean;
    maxFileSize?: number;
    allowedFileTypes?: string[];
    enableFeedback?: boolean;
    enableTypingIndicator?: boolean;
  };
}
```

### ChatMessage

Message object structure.

```typescript
interface ChatMessage {
  id: string;
  type: 'user' | 'bot' | 'system';
  content: string;
  timestamp: Date;
  conversationId: string;
  userId?: string;
  metadata?: Record<string, any>;
  attachments?: MessageAttachment[];
}
```

### MessageAttachment

File attachment structure.

```typescript
interface MessageAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
}
```

### ThemeConfig

Custom theme configuration.

```typescript
interface ThemeConfig {
  primaryColor?: string;
  primaryHover?: string;
  primaryActive?: string;
  primaryLight?: string;
  backgroundColor?: string;
  surfaceColor?: string;
  borderColor?: string;
  textPrimary?: string;
  textSecondary?: string;
  textMuted?: string;
  textInverse?: string;
  userMessageBg?: string;
  userMessageText?: string;
  botMessageBg?: string;
  botMessageText?: string;
  botMessageBorder?: string;
  fontFamily?: string;
  fontSize?: string;
  fontWeight?: string;
  lineHeight?: string;
  borderRadius?: string;
  spacing?: string;
  shadow?: string;
  shadowLg?: string;
  customVariables?: Record<string, string>;
}
```

### DetectionReport

Environment detection report.

```typescript
interface DetectionReport {
  recommendedMode: 'widget' | 'iframe';
  conflicts: ConflictInfo[];
  capabilities: {
    cssSupport: boolean;
    jsSupport: boolean;
    iframeSupport: boolean;
    localStorageSupport: boolean;
  };
  environment: {
    userAgent: string;
    viewport: { width: number; height: number };
    colorScheme: 'light' | 'dark' | 'no-preference';
    reducedMotion: boolean;
  };
}
```

### ConflictInfo

CSS/JS conflict information.

```typescript
interface ConflictInfo {
  type: 'css' | 'js' | 'dom';
  severity: 'low' | 'medium' | 'high';
  description: string;
  selector?: string;
  property?: string;
  value?: string;
  recommendation: string;
}
```

### MountInfo

Information about widget mounting.

```typescript
interface MountInfo {
  mode: 'widget' | 'iframe';
  container: HTMLElement;
  botConfig: BotConfig;
  timestamp: Date;
  loadTime: number;
}
```

## Events

### Widget Events

#### 'ready'
Fired when the widget is fully loaded and ready for interaction.

```javascript
widget.on('ready', () => {
  console.log('Widget is ready');
});
```

#### 'mounted'
Fired when the widget is successfully mounted to the DOM.

```javascript
widget.on('mounted', (info: MountInfo) => {
  console.log('Widget mounted in mode:', info.mode);
});
```

#### 'unmounted'
Fired when the widget is unmounted from the DOM.

```javascript
widget.on('unmounted', () => {
  console.log('Widget unmounted');
});
```

#### 'message'
Fired when a new message is sent or received.

```javascript
widget.on('message', (message: ChatMessage) => {
  console.log('New message:', message);
});
```

#### 'conversation-started'
Fired when a new conversation begins.

```javascript
widget.on('conversation-started', (conversationId: string) => {
  console.log('Conversation started:', conversationId);
});
```

#### 'conversation-ended'
Fired when a conversation ends.

```javascript
widget.on('conversation-ended', (conversationId: string) => {
  console.log('Conversation ended:', conversationId);
});
```

#### 'bot-loaded'
Fired when bot configuration is loaded from the API.

```javascript
widget.on('bot-loaded', (botConfig: BotConfig) => {
  console.log('Bot loaded:', botConfig.name);
});
```

#### 'error'
Fired when an error occurs.

```javascript
widget.on('error', (error: Error) => {
  console.error('Widget error:', error);
});
```

#### 'theme-changed'
Fired when the theme is changed.

```javascript
widget.on('theme-changed', (theme: string) => {
  console.log('Theme changed to:', theme);
});
```

## Error Handling

### Error Types

#### ConfigurationError
Thrown when widget configuration is invalid.

```javascript
try {
  const widget = new ProcmsChatbotWidget({
    // Missing required botUuid
    apiKey: 'pk_live_xxx'
  });
} catch (error) {
  if (error instanceof ConfigurationError) {
    console.error('Configuration error:', error.message);
  }
}
```

#### APIError
Thrown when API requests fail.

```javascript
widget.on('error', (error) => {
  if (error instanceof APIError) {
    console.error('API error:', error.status, error.message);
  }
});
```

#### MountError
Thrown when widget fails to mount.

```javascript
try {
  await widget.mount('#non-existent-element');
} catch (error) {
  if (error instanceof MountError) {
    console.error('Mount error:', error.message);
  }
}
```

### Error Recovery

```javascript
widget.on('error', (error) => {
  console.error('Widget error:', error);
  
  // Attempt recovery based on error type
  if (error instanceof APIError && error.status === 401) {
    // Refresh API key
    widget.updateConfig({ apiKey: getNewApiKey() });
  } else if (error instanceof MountError) {
    // Retry mounting
    setTimeout(() => {
      widget.mount('#container');
    }, 1000);
  }
});
```

## Best Practices

1. **Always handle errors**: Implement error event listeners
2. **Cleanup resources**: Call unmount() or destroy() when done
3. **Use TypeScript**: Import type definitions for better DX
4. **Monitor performance**: Track loading times and errors
5. **Test thoroughly**: Test on different browsers and devices
6. **Follow CSP**: Ensure Content Security Policy compatibility
7. **Optimize loading**: Use lazy loading for non-critical pages

## Migration Guide

### From v1.x to v2.x

#### Breaking Changes
- `ProcmsWidget` renamed to `ProcmsChatbotWidget`
- Configuration structure changed
- Event names updated

#### Migration Steps
```javascript
// Old (v1.x)
const widget = new ProcmsWidget({
  bot: 'bot-uuid',
  key: 'api-key'
});

// New (v2.x)
const widget = new ProcmsChatbotWidget({
  botUuid: 'bot-uuid',
  apiKey: 'api-key'
});
```

See [Migration Guide](./migration.md) for complete details.
